import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../../lib/api/client';
import { Trip } from '../../types';

// Trip queries
export const useTrips = (clientId?: string) => {
  return useQuery({
    queryKey: ['trips', clientId],
    queryFn: async () => {
      // Use mock server for development
      if (process.env.NODE_ENV === 'development') {
        const { mockAPIServer } = await import('../../lib/api/mockServer');
        const response = await mockAPIServer.getTrips(clientId);
        return response.data;
      }
      
      const endpoint = clientId ? `/trips?clientId=${clientId}` : '/trips';
      const response = await apiClient.get<Trip[]>(endpoint);
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    enabled: !!clientId, // Only run if clientId is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors
      if (error.message.includes('401') || error.message.includes('403')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

export const useTrip = (tripId: string) => {
  return useQuery({
    queryKey: ['trip', tripId],
    queryFn: async () => {
      const response = await apiClient.get<Trip>(`/trips/${tripId}`);
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    enabled: !!tripId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Trip mutations
export const useCreateTrip = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (tripData: Partial<Trip>) => {
      const response = await apiClient.post<Trip>('/trips', tripData);
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    onSuccess: (newTrip) => {
      // Invalidate trips list
      queryClient.invalidateQueries(['trips']);
      
      // Add the new trip to cache
      queryClient.setQueryData(['trip', newTrip.id], newTrip);
      
      // Update trips list cache if it exists
      queryClient.setQueryData(['trips', newTrip.clientId], (oldTrips: Trip[] | undefined) => {
        if (!oldTrips) return [newTrip];
        return [newTrip, ...oldTrips];
      });
    },
    onError: (error) => {
      console.error('Failed to create trip:', error);
    },
  });
};

export const useUpdateTrip = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ tripId, updates }: { tripId: string; updates: Partial<Trip> }) => {
      const response = await apiClient.patch<Trip>(`/trips/${tripId}`, updates);
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    onSuccess: (updatedTrip) => {
      // Update individual trip cache
      queryClient.setQueryData(['trip', updatedTrip.id], updatedTrip);
      
      // Update trips list cache
      queryClient.setQueryData(['trips', updatedTrip.clientId], (oldTrips: Trip[] | undefined) => {
        if (!oldTrips) return [updatedTrip];
        return oldTrips.map(trip => 
          trip.id === updatedTrip.id ? updatedTrip : trip
        );
      });
      
      // Invalidate related queries
      queryClient.invalidateQueries(['trips']);
    },
  });
};

export const useDeleteTrip = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (tripId: string) => {
      // Use mock server for development
      if (process.env.NODE_ENV === 'development') {
        const { mockAPIServer } = await import('../../lib/api/mockServer');
        const response = await mockAPIServer.deleteTrip(tripId);
        if (!response.success) {
          throw new Error('Failed to delete trip');
        }
        return tripId;
      }
      
      const response = await apiClient.delete(`/trips/${tripId}`);
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return tripId;
    },
    onSuccess: (deletedTripId) => {
      // Remove from all caches
      queryClient.removeQueries({ queryKey: ['trip', deletedTripId] });
      
      // Update trips lists
      queryClient.setQueriesData({ queryKey: ['trips'] }, (oldTrips: Trip[] | undefined) => {
        if (!oldTrips) return [];
        return oldTrips.filter(trip => trip.id !== deletedTripId);
      });
      
      // Invalidate trips queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['trips'] });
    },
  });
};

// Trip stats hook
export const useTripStats = (clientId: string) => {
  return useQuery({
    queryKey: ['tripStats', clientId],
    queryFn: async () => {
      // Use mock server for development
      if (process.env.NODE_ENV === 'development') {
        const { mockAPIServer } = await import('../../lib/api/mockServer');
        const response = await mockAPIServer.getTripStats(clientId);
        return response.data;
      }
      
      const response = await apiClient.get<{
        activeTrips: number;
        upcomingTrips: number;
        totalDestinations: number;
        totalExperiences: number;
      }>(`/trips/stats?clientId=${clientId}`);
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    enabled: !!clientId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Featured destinations hook
export const useFeaturedDestinations = () => {
  return useQuery({
    queryKey: ['featuredDestinations'],
    queryFn: async () => {
      // Use mock server for development
      if (process.env.NODE_ENV === 'development') {
        const { mockAPIServer } = await import('../../lib/api/mockServer');
        const response = await mockAPIServer.getFeaturedDestinations();
        return response.data;
      }
      
      const response = await apiClient.get<Array<{
        id: string;
        title: string;
        image: string;
        description: string;
        price: string;
      }>>('/destinations/featured');
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    staleTime: 30 * 60 * 1000, // 30 minutes (featured destinations change less frequently)
    gcTime: 60 * 60 * 1000, // 1 hour
  });
};

// Trip inquiry mutation
export const useCreateTripInquiry = () => {
  return useMutation({
    mutationFn: async (data: {
      clientId: string;
      context: string;
      preferences?: Record<string, any>;
    }) => {
      // Use mock server for development
      if (process.env.NODE_ENV === 'development') {
        const { mockAPIServer } = await import('../../lib/api/mockServer');
        const response = await mockAPIServer.createTripInquiry(data);
        return response.data;
      }
      
      const response = await apiClient.post<{ inquiryId: string }>('/inquiries', data);
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    onSuccess: (data) => {
      console.log('Trip inquiry created:', data.inquiryId);
    },
    onError: (error) => {
      console.error('Failed to create trip inquiry:', error);
    },
  });
};

// Additional hooks for enhanced ClientDashboard

/**
 * Hook for dashboard statistics with real-time updates
 */
export const useTripStats = (clientId: string) => {
  return useQuery({
    queryKey: ['tripStats', clientId],
    queryFn: async () => {
      const response = await apiClient.get<{
        activeTrips: number;
        upcomingTrips: number;
        totalDestinations: number;
        totalExperiences: number;
      }>(`/trips/stats?clientId=${clientId}`);
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    enabled: !!clientId,
    staleTime: 1 * 60 * 1000, // 1 minute - stats should be current
    cacheTime: 5 * 60 * 1000,
    refetchInterval: 5 * 60 * 1000, // Auto-refresh every 5 minutes for luxury UX
  });
};

/**
 * Hook for featured destinations with smart caching
 */
export const useFeaturedDestinations = () => {
  return useQuery({
    queryKey: ['featuredDestinations'],
    queryFn: async () => {
      const response = await apiClient.get<{
        title: string;
        image: string;
        description: string;
        price: string;
        id: string;
      }[]>('/destinations/featured?limit=3');
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    staleTime: 30 * 60 * 1000, // 30 minutes - featured content changes less frequently
    cacheTime: 60 * 60 * 1000, // 1 hour
    retry: 2,
  });
};

/**
 * Mutation for creating trip inquiries from AI chat
 */
export const useCreateTripInquiry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: {
      clientId: string;
      context: string;
      preferences?: Record<string, any>;
    }) => {
      const response = await apiClient.post<{ inquiryId: string }>('/trips/inquiries', data);
      
      if (response.error) {
        throw new Error(response.error.message);
      }
      
      return response.data!;
    },
    onSuccess: (data, variables) => {
      // Invalidate trips to show new inquiry
      queryClient.invalidateQueries(['trips', variables.clientId]);
      queryClient.invalidateQueries(['tripStats', variables.clientId]);
    },
    onError: (error) => {
      console.error('Failed to create trip inquiry:', error);
    },
  });
};