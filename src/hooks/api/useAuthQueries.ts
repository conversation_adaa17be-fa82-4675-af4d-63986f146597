import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { User } from '../../types';
import { apiClient } from '../../lib/api/authClient';

// Authentication-related React Query hooks

interface LoginCredentials {
  email: string;
  password: string;
}

interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// Query to get current user profile
export const useCurrentUser = () => {
  return useQuery({
    queryKey: ['auth', 'currentUser'],
    queryFn: async (): Promise<User> => {
      return apiClient.get<User>('/auth/me');
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 401 errors (authentication failures)
      if (error?.status === 401) return false;
      return failureCount < 2;
    },
  });
};

// Mutation for login
export const useLogin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (credentials: LoginCredentials): Promise<LoginResponse> => {
      return apiClient.post<LoginResponse>('/auth/login', credentials);
    },
    onSuccess: (data) => {
      // Store tokens
      localStorage.setItem('access_token', data.accessToken);
      localStorage.setItem('refresh_token', data.refreshToken);
      
      // Cache user data
      queryClient.setQueryData(['auth', 'currentUser'], data.user);
      
      // Invalidate and refetch any user-specific data
      queryClient.invalidateQueries({ queryKey: ['trips'] });
      queryClient.invalidateQueries({ queryKey: ['quotes'] });
    },
    onError: (error) => {
      console.error('Login failed:', error);
      // Clear any stale auth data
      queryClient.removeQueries({ queryKey: ['auth'] });
    },
  });
};

// Mutation for logout
export const useLogout = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (): Promise<void> => {
      const token = localStorage.getItem('access_token');
      if (token) {
        await apiClient.post('/auth/logout', {});
      }
    },
    onSettled: () => {
      // Always clear local storage and cache on logout
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      
      // Clear all cached data
      queryClient.clear();
      
      // Redirect to login
      window.location.href = '/login';
    },
  });
};

// Mutation for updating user profile
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (profileData: Partial<User>): Promise<User> => {
      return apiClient.put<User>('/auth/profile', profileData);
    },
    onSuccess: (updatedUser) => {
      // Update cached user data
      queryClient.setQueryData(['auth', 'currentUser'], updatedUser);
      
      // Show success notification
      // You can integrate with your notification system here
    },
    onError: (error) => {
      console.error('Profile update failed:', error);
      // Show error notification
    },
  });
};

// Query for user preferences (cached separately for performance)
export const useUserPreferences = () => {
  return useQuery({
    queryKey: ['auth', 'preferences'],
    queryFn: async () => {
      const user = await apiClient.get<User>('/auth/me');
      return user.preferences;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - preferences don't change often
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Mutation for updating preferences only
export const useUpdatePreferences = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (preferences: User['preferences']): Promise<User['preferences']> => {
      const response = await apiClient.put<{ preferences: User['preferences'] }>('/auth/preferences', { preferences });
      return response.preferences;
    },
    onSuccess: (updatedPreferences) => {
      // Update cached preferences
      queryClient.setQueryData(['auth', 'preferences'], updatedPreferences);
      
      // Update the full user object if it's cached
      queryClient.setQueryData(['auth', 'currentUser'], (oldUser: User | undefined) => {
        if (oldUser) {
          return { ...oldUser, preferences: updatedPreferences };
        }
        return oldUser;
      });
    },
  });
};