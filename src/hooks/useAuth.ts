import { useState, useEffect, createContext, useContext } from 'react';
import { User } from '../types';

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Mock authentication hook for MVP
export function useAuthState() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading and check for stored user
    const storedUser = localStorage.getItem('vvip_user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    setLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    // Mock authentication with proper email/password validation
    const validCredentials = [
      { email: '<EMAIL>', password: 'password', role: 'vvip' as const, name: 'Alexander Rothschild' },
      { email: '<EMAIL>', password: 'password', role: 'admin' as const, name: 'Sarah Mitchell' },
      { email: '<EMAIL>', password: 'password', role: 'partner' as const, name: 'Hotel Concierge' }
    ];

    // Find matching credentials
    const validUser = validCredentials.find(
      cred => cred.email === email && cred.password === password
    );

    if (!validUser) {
      throw new Error('Invalid email or password');
    }

    // Create user object based on validated credentials
    const mockUser: User = {
      id: validUser.role === 'admin' ? '1' : validUser.role === 'partner' ? '2' : '3',
      email: validUser.email,
      name: validUser.name,
      role: validUser.role,
      avatar: validUser.role === 'admin' 
        ? 'https://images.pexels.com/photos/3785079/pexels-photo-3785079.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop'
        : validUser.role === 'partner'
        ? 'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop'
        : 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
      preferences: {
        dietary: ['Vegetarian', 'Gluten-free'],
        loyaltyPrograms: ['Marriott Bonvoy', 'American Express'],
        travelStyle: 'Ultra-luxury',
        preferredBrands: ['Four Seasons', 'Ritz-Carlton', 'Aman'],
        likes: ['Private jets', 'Michelin dining', 'Spa treatments'],
        dislikes: ['Crowds', 'Budget options', 'Long layovers']
      }
    };
    
    localStorage.setItem('vvip_user', JSON.stringify(mockUser));
    setUser(mockUser);
  };

  const logout = () => {
    localStorage.removeItem('vvip_user');
    setUser(null);
  };

  return { user, login, logout, loading };
}