import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { errorHandler, ErrorType, AppError } from '../lib/errors/errorHandler';
import { useToast } from './useToast';

export interface ErrorHandlingOptions {
  showToast?: boolean;
  retryable?: boolean;
  critical?: boolean;
  context?: Record<string, any>;
}

export function useErrorHandling() {
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  const handleError = useCallback((
    error: Error | AppError,
    options: ErrorHandlingOptions = {}
  ) => {
    const {
      showToast: shouldShowToast = true,
      retryable = false,
      critical = false,
      context = {}
    } = options;

    let appError: AppError;

    // Convert regular Error to AppError if needed
    if ('type' in error) {
      appError = error as AppError;
    } else {
      appError = errorHandler.createError(
        ErrorType.UNKNOWN_ERROR,
        'UNHANDLED_ERROR',
        error.message,
        { stack: error.stack },
        context
      );
    }

    // Report error to error handler
    errorHandler.handleError(appError);

    // Show toast notification if requested
    if (shouldShowToast) {
      showToast({
        type: critical ? 'error' : 'warning',
        title: getErrorTitle(appError),
        message: appError.message,
        duration: critical ? 0 : 5000, // Critical errors don't auto-dismiss
      });
    }

    // Handle authentication errors
    if (appError.type === ErrorType.AUTHENTICATION_ERROR) {
      // Clear React Query cache
      queryClient.clear();
      
      // Redirect to login
      setTimeout(() => {
        window.location.href = '/login';
      }, 1000);
    }

    // Handle network errors
    if (appError.type === ErrorType.NETWORK_ERROR) {
      // Invalidate all queries to trigger refetch when connection is restored
      queryClient.invalidateQueries();
    }

    return appError;
  }, [queryClient, showToast]);

  const handleApiError = useCallback((
    response: Response,
    requestId?: string,
    options: ErrorHandlingOptions = {}
  ) => {
    const error = errorHandler.handleApiError(response, requestId);
    return handleError(error, options);
  }, [handleError]);

  const handleNetworkError = useCallback((
    error: Error,
    requestId?: string,
    options: ErrorHandlingOptions = {}
  ) => {
    const networkError = errorHandler.handleNetworkError(error, requestId);
    return handleError(networkError, options);
  }, [handleError]);

  const createError = useCallback((
    type: ErrorType,
    code: string,
    message: string,
    details?: any,
    context?: Record<string, any>
  ) => {
    return errorHandler.createError(type, code, message, details, context);
  }, []);

  const clearErrors = useCallback(() => {
    errorHandler.clearErrorQueue();
  }, []);

  return {
    handleError,
    handleApiError,
    handleNetworkError,
    createError,
    clearErrors,
  };
}

function getErrorTitle(error: AppError): string {
  switch (error.type) {
    case ErrorType.NETWORK_ERROR:
      return 'Connection Issue';
    case ErrorType.AUTHENTICATION_ERROR:
      return 'Authentication Required';
    case ErrorType.AUTHORIZATION_ERROR:
      return 'Access Denied';
    case ErrorType.VALIDATION_ERROR:
      return 'Invalid Input';
    case ErrorType.API_ERROR:
      return 'Service Error';
    case ErrorType.BUSINESS_LOGIC_ERROR:
      return 'Request Failed';
    default:
      return 'Unexpected Error';
  }
}

// Hook for handling React Query errors specifically
export function useQueryErrorHandler() {
  const { handleError } = useErrorHandling();

  return useCallback((error: Error, query: any) => {
    const context = {
      queryKey: query.queryKey,
      queryHash: query.queryHash,
      state: query.state,
    };

    // Determine error type based on error properties
    let errorType = ErrorType.UNKNOWN_ERROR;
    if (error.message.includes('fetch') || error.message.includes('network')) {
      errorType = ErrorType.NETWORK_ERROR;
    } else if (error.message.includes('401') || error.message.includes('unauthorized')) {
      errorType = ErrorType.AUTHENTICATION_ERROR;
    } else if (error.message.includes('403') || error.message.includes('forbidden')) {
      errorType = ErrorType.AUTHORIZATION_ERROR;
    } else if (error.message.includes('400') || error.message.includes('validation')) {
      errorType = ErrorType.VALIDATION_ERROR;
    } else if (error.message.includes('500') || error.message.includes('server')) {
      errorType = ErrorType.API_ERROR;
    }

    const appError = handleError(error, {
      showToast: errorType !== ErrorType.NETWORK_ERROR, // Don't show toast for network errors in queries
      context,
    });

    return appError;
  }, [handleError]);
}

// Hook for handling mutation errors
export function useMutationErrorHandler() {
  const { handleError } = useErrorHandling();

  return useCallback((error: Error, variables: any, context: any) => {
    const errorContext = {
      mutationVariables: variables,
      mutationContext: context,
    };

    return handleError(error, {
      showToast: true, // Always show toast for mutation errors
      critical: true, // Mutation errors are usually critical
      context: errorContext,
    });
  }, [handleError]);
}