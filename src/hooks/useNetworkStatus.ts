import { useState, useEffect } from 'react';

export interface NetworkStatus {
  isOnline: boolean;
  isSlowConnection: boolean;
  connectionType: string;
  effectiveType: string;
}

export function useNetworkStatus(): NetworkStatus {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    isSlowConnection: false,
    connectionType: 'unknown',
    effectiveType: 'unknown',
  });

  useEffect(() => {
    const updateNetworkStatus = () => {
      const connection = (navigator as any).connection || 
                        (navigator as any).mozConnection || 
                        (navigator as any).webkitConnection;

      const isSlowConnection = connection ? 
        connection.effectiveType === 'slow-2g' || 
        connection.effectiveType === '2g' : false;

      setNetworkStatus({
        isOnline: navigator.onLine,
        isSlowConnection,
        connectionType: connection?.type || 'unknown',
        effectiveType: connection?.effectiveType || 'unknown',
      });
    };

    const handleOnline = () => updateNetworkStatus();
    const handleOffline = () => updateNetworkStatus();
    const handleConnectionChange = () => updateNetworkStatus();

    // Initial check
    updateNetworkStatus();

    // Event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    if (connection) {
      connection.addEventListener('change', handleConnectionChange);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if (connection) {
        connection.removeEventListener('change', handleConnectionChange);
      }
    };
  }, []);

  return networkStatus;
}

// Hook for simulating network conditions (development only)
export function useNetworkThrottling() {
  const [isThrottled, setIsThrottled] = useState(false);
  const [throttleType, setThrottleType] = useState<'slow-3g' | 'fast-3g' | 'offline'>('slow-3g');

  const enableThrottling = (type: 'slow-3g' | 'fast-3g' | 'offline') => {
    if (process.env.NODE_ENV === 'development') {
      setThrottleType(type);
      setIsThrottled(true);
      
      // In a real implementation, you might use Chrome DevTools Protocol
      // or a service worker to actually throttle network requests
      console.warn(`Network throttling enabled: ${type}`);
    }
  };

  const disableThrottling = () => {
    setIsThrottled(false);
    console.log('Network throttling disabled');
  };

  return {
    isThrottled,
    throttleType,
    enableThrottling,
    disableThrottling,
  };
}