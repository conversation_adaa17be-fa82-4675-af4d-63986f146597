import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Plus, Search, Filter, Users, TrendingUp, Calendar, DollarSign, Eye } from 'lucide-react';
import { mockTrips, mockPartners, mockQuotes } from '../../data/mockData';
import { formatCurrency, formatDate } from '../../lib/utils';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';
import { QuoteBuilder } from './QuoteBuilder';

export function AdminDashboard() {
  const [activeView, setActiveView] = useState<'overview' | 'quotes' | 'partners' | 'analytics'>('overview');
  const [showQuoteBuilder, setShowQuoteBuilder] = useState(false);

  const stats = [
    { label: 'Active Quotes', value: '23', change: '+12%', icon: Calendar, color: 'bg-blue-500' },
    { label: 'Total Revenue', value: '$2.4M', change: '+8%', icon: DollarSign, color: 'bg-green-500' },
    { label: 'Partners', value: '156', change: '+5%', icon: Users, color: 'bg-purple-500' },
    { label: 'Conversion Rate', value: '94%', change: '+2%', icon: TrendingUp, color: 'bg-champagne-500' }
  ];

  if (showQuoteBuilder) {
    return <QuoteBuilder onClose={() => setShowQuoteBuilder(false)} />;
  }

  return (
    <div className="min-h-screen bg-luxury-gradient-light dark:bg-luxury-gradient transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-12"
        >
          <div className="flex justify-between items-start">
            <div className="space-y-3">
              <h1 className="text-display-lg font-serif font-bold text-platinum-50 text-balance">Admin Dashboard</h1>
              <p className="text-body-xl text-platinum-300 max-w-2xl">Manage quotes, partners, and luxury experiences with precision</p>
            </div>
            <Button onClick={() => setShowQuoteBuilder(true)} size="lg">
              <Plus className="w-5 h-5 mr-3" />
              New Quote
            </Button>
          </div>
        </motion.div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-platinum-900/95 p-1 rounded-xl border border-champagne-500/20">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'quotes', label: 'Quote Management' },
              { id: 'partners', label: 'Partners' },
              { id: 'analytics', label: 'Analytics' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id as any)}
                className={`px-6 py-3 rounded-lg font-medium transition-all ${
                  activeView === tab.id
                    ? 'bg-champagne-500 text-platinum-900 shadow-sm'
                    : 'text-platinum-300 hover:text-platinum-50 hover:bg-platinum-800/50'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 rounded-2xl ${stat.color} flex items-center justify-center`}>
                    <stat.icon className="w-6 h-6 text-platinum-50" />
                  </div>
                  <span className="text-champagne-400 text-sm font-medium">{stat.change}</span>
                </div>
                <div className="space-y-2">
                  <p className="text-caption-md text-platinum-400 uppercase tracking-wider">{stat.label}</p>
                  <p className="text-display-sm font-bold text-platinum-50 tabular-numbers">{stat.value}</p>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Content based on active view */}
        {activeView === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Quotes */}
            <Card className="p-8">
              <div className="mb-8">
                <h3 className="text-heading-xl font-serif font-bold text-platinum-50 mb-2">Recent Quotes</h3>
                <p className="text-body-lg text-platinum-400">Latest luxury travel proposals</p>
              </div>
              <div className="space-y-4">
                {mockTrips.slice(0, 3).map((trip) => (
                  <div key={trip.id} className="flex items-center justify-between p-6 bg-platinum-800/30 rounded-xl border border-platinum-700/30">
                    <div className="space-y-2">
                      <h4 className="text-heading-md font-semibold text-platinum-50">{trip.title}</h4>
                      <p className="text-body-md text-platinum-300">{trip.destination}</p>
                      <p className="text-caption-md text-platinum-400 uppercase tracking-wider">{formatDate(trip.startDate)}</p>
                    </div>
                    <div className="text-right space-y-2">
                      <p className="text-price-md font-serif text-champagne-400 luxury-numbers">{formatCurrency(trip.totalCost, { style: 'elegant' })}</p>
                      <span className={`inline-block px-3 py-1 rounded-full text-caption-md font-medium uppercase tracking-wider ${
                        trip.status === 'proposed' ? 'bg-champagne-500/20 text-champagne-400 border border-champagne-500/30' : 'bg-green-500/20 text-green-400 border border-green-500/30'
                      }`}>
                        {trip.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Top Partners */}
            <Card className="p-8">
              <div className="mb-8">
                <h3 className="text-heading-xl font-serif font-bold text-platinum-50 mb-2">Top Partners</h3>
                <p className="text-body-lg text-platinum-400">Premium service providers</p>
              </div>
              <div className="space-y-4">
                {mockPartners.map((partner) => (
                  <div key={partner.id} className="flex items-center justify-between p-4 bg-platinum-800/30 rounded-xl">
                    <div className="flex items-center space-x-3">
                      <img
                        src={partner.avatar}
                        alt={partner.name}
                        className="w-10 h-10 rounded-xl object-cover"
                      />
                      <div>
                        <h4 className="font-semibold text-platinum-50">{partner.name}</h4>
                        <p className="text-sm text-platinum-300">{partner.location}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-2">
                        <span className="text-champagne-400 font-semibold">{partner.qualityScore}/10</span>
                        <span className="text-xs text-platinum-400">{partner.responseTime}h avg</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        )}

        {activeView === 'quotes' && (
          <div className="space-y-6">
            {/* Search and Filters */}
            <Card className="p-6">
              <div className="flex space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search quotes..."
                    className="w-full"
                  />
                </div>
                <Button variant="outline">
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
                </Button>
                <Button variant="outline">
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </div>
            </Card>

            {/* Quotes Table */}
            <Card className="p-6">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-champagne-500/20">
                      <th className="text-left py-4 px-2 font-semibold text-platinum-50">Trip</th>
                      <th className="text-left py-4 px-2 font-semibold text-platinum-50">Client</th>
                      <th className="text-left py-4 px-2 font-semibold text-platinum-50">Dates</th>
                      <th className="text-left py-4 px-2 font-semibold text-platinum-50">Value</th>
                      <th className="text-left py-4 px-2 font-semibold text-platinum-50">Status</th>
                      <th className="text-left py-4 px-2 font-semibold text-platinum-50">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {mockTrips.map((trip) => (
                      <tr key={trip.id} className="border-b border-platinum-700/30 hover:bg-platinum-800/20">
                        <td className="py-4 px-2">
                          <div>
                            <h4 className="font-medium text-platinum-50">{trip.title}</h4>
                            <p className="text-sm text-platinum-300">{trip.destination}</p>
                          </div>
                        </td>
                        <td className="py-4 px-2">
                          <span className="text-platinum-50">VVIP Client</span>
                        </td>
                        <td className="py-4 px-2">
                          <span className="text-platinum-300">{formatDate(trip.startDate)}</span>
                        </td>
                        <td className="py-4 px-2">
                          <span className="font-semibold text-platinum-50">{formatCurrency(trip.totalCost)}</span>
                        </td>
                        <td className="py-4 px-2">
                          <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                            trip.status === 'proposed' ? 'bg-champagne-500/20 text-champagne-400' :
                            trip.status === 'confirmed' ? 'bg-green-500/20 text-green-400' :
                            'bg-platinum-600/20 text-platinum-300'
                          }`}>
                            {trip.status.charAt(0).toUpperCase() + trip.status.slice(1)}
                          </span>
                        </td>
                        <td className="py-4 px-2">
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}