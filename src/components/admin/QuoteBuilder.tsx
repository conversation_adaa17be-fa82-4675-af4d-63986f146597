import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Plus, Trash2, Calendar, DollarSign, Clock, MapPin, Users, Save, Send } from 'lucide-react';
import { mockPartners } from '../../data/mockData';
import { Activity, Accommodation, ItineraryDay } from '../../types';
import { formatCurrency } from '../../lib/utils';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';

interface QuoteBuilderProps {
  onClose: () => void;
}

export function QuoteBuilder({ onClose }: QuoteBuilderProps) {
  const [tripDetails, setTripDetails] = useState({
    title: '',
    destination: '',
    startDate: '',
    endDate: '',
    guestCount: 2,
    budget: ''
  });

  const [itinerary, setItinerary] = useState<ItineraryDay[]>([
    {
      id: '1',
      day: 1,
      date: new Date(),
      title: 'Arrival Day',
      description: '',
      activities: [],
      images: []
    }
  ]);

  const [selectedPartner, setSelectedPartner] = useState<string | null>(null);
  const [showPartnerSelector, setShowPartnerSelector] = useState(false);

  const addDay = () => {
    const newDay: ItineraryDay = {
      id: (itinerary.length + 1).toString(),
      day: itinerary.length + 1,
      date: new Date(),
      title: `Day ${itinerary.length + 1}`,
      description: '',
      activities: [],
      images: []
    };
    setItinerary([...itinerary, newDay]);
  };

  const addActivity = (dayId: string) => {
    if (!selectedPartner) return;
    
    const partner = mockPartners.find(p => p.id === selectedPartner);
    if (!partner) return;

    const newActivity: Activity = {
      id: `activity-${Date.now()}`,
      partnerId: selectedPartner,
      title: 'New Experience',
      description: 'Luxury experience description',
      startTime: '09:00',
      endTime: '12:00',
      cost: 5000,
      images: partner.media,
      location: partner.location,
      partnerName: partner.name
    };

    setItinerary(itinerary.map(day => 
      day.id === dayId 
        ? { ...day, activities: [...day.activities, newActivity] }
        : day
    ));
    setShowPartnerSelector(false);
    setSelectedPartner(null);
  };

  const removeActivity = (dayId: string, activityId: string) => {
    setItinerary(itinerary.map(day => 
      day.id === dayId 
        ? { ...day, activities: day.activities.filter(a => a.id !== activityId) }
        : day
    ));
  };

  const calculateTotal = () => {
    return itinerary.reduce((total, day) => 
      total + day.activities.reduce((dayTotal, activity) => dayTotal + activity.cost, 0) +
      (day.accommodation?.cost || 0), 0
    );
  };

  return (
    <div className="min-h-screen bg-luxury-gradient">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-serif font-bold text-platinum-50 mb-2">Quote Builder</h1>
            <p className="text-platinum-300">Create premium luxury travel experiences</p>
          </div>
          <div className="flex space-x-4">
            <Button variant="outline" onClick={onClose}>Cancel</Button>
            <Button variant="secondary">
              <Save className="w-4 h-4 mr-2" />
              Save Draft
            </Button>
            <Button>
              <Send className="w-4 h-4 mr-2" />
              Send Quote
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Trip Details */}
          <div className="lg:col-span-1">
            <Card className="p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-platinum-50 mb-4">Trip Details</h3>
              <div className="space-y-4">
                <Input
                  label="Trip Title"
                  value={tripDetails.title}
                  onChange={(e) => setTripDetails({...tripDetails, title: e.target.value})}
                  placeholder="Mediterranean Odyssey"
                />
                <Input
                  label="Destination"
                  value={tripDetails.destination}
                  onChange={(e) => setTripDetails({...tripDetails, destination: e.target.value})}
                  placeholder="French Riviera & Italian Coast"
                />
                <div className="grid grid-cols-2 gap-3">
                  <Input
                    label="Start Date"
                    type="date"
                    value={tripDetails.startDate}
                    onChange={(e) => setTripDetails({...tripDetails, startDate: e.target.value})}
                  />
                  <Input
                    label="End Date"
                    type="date"
                    value={tripDetails.endDate}
                    onChange={(e) => setTripDetails({...tripDetails, endDate: e.target.value})}
                  />
                </div>
                <Input
                  label="Guests"
                  type="number"
                  value={tripDetails.guestCount}
                  onChange={(e) => setTripDetails({...tripDetails, guestCount: parseInt(e.target.value)})}
                />
                <Input
                  label="Budget Range"
                  value={tripDetails.budget}
                  onChange={(e) => setTripDetails({...tripDetails, budget: e.target.value})}
                  placeholder="$100,000 - $150,000"
                />
              </div>

              <div className="mt-6 pt-6 border-t border-slate-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-platinum-300">Estimated Total</span>
                  <span className="text-2xl font-bold text-platinum-50">{formatCurrency(calculateTotal())}</span>
                </div>
                <div className="text-xs text-platinum-400">
                  {itinerary.length} days • {itinerary.reduce((total, day) => total + day.activities.length, 0)} experiences
                </div>
              </div>
            </Card>
          </div>

          {/* Itinerary Builder */}
          <div className="lg:col-span-3">
            <div className="space-y-6">
              {itinerary.map((day, index) => (
                <motion.div
                  key={day.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-serif font-bold text-platinum-50">
                          Day {day.day}
                        </h3>
                        <input
                          type="text"
                          value={day.title}
                          onChange={(e) => {
                            const updatedItinerary = itinerary.map(d => 
                              d.id === day.id ? { ...d, title: e.target.value } : d
                            );
                            setItinerary(updatedItinerary);
                          }}
                          className="mt-1 text-sm text-slate-600 border-none bg-transparent focus:outline-none focus:ring-2 focus:ring-amber-400 rounded px-2 py-1"
                          placeholder="Day title..."
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowPartnerSelector(true)}
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add Experience
                      </Button>
                    </div>

                    {/* Activities */}
                    <div className="space-y-4">
                      {day.activities.map((activity) => (
                        <div key={activity.id} className="bg-platinum-800/30 rounded-xl p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <input
                                type="text"
                                value={activity.title}
                                onChange={(e) => {
                                  const updatedItinerary = itinerary.map(d => 
                                    d.id === day.id 
                                      ? {
                                          ...d,
                                          activities: d.activities.map(a => 
                                            a.id === activity.id ? { ...a, title: e.target.value } : a
                                          )
                                        }
                                      : d
                                  );
                                  setItinerary(updatedItinerary);
                                }}
                                className="font-semibold text-platinum-50 bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-champagne-400 rounded px-2 py-1 w-full"
                              />
                              <p className="text-sm text-champagne-400 font-medium">{activity.partnerName}</p>
                              <div className="flex items-center space-x-4 text-xs text-platinum-400 mt-1">
                                <span className="flex items-center">
                                  <Clock className="w-3 h-3 mr-1" />
                                  {activity.startTime} - {activity.endTime}
                                </span>
                                <span className="flex items-center">
                                  <MapPin className="w-3 h-3 mr-1" />
                                  {activity.location}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center space-x-3">
                              <div className="text-right">
                                <input
                                  type="number"
                                  value={activity.cost}
                                  onChange={(e) => {
                                    const updatedItinerary = itinerary.map(d => 
                                      d.id === day.id 
                                        ? {
                                            ...d,
                                            activities: d.activities.map(a => 
                                              a.id === activity.id ? { ...a, cost: parseInt(e.target.value) || 0 } : a
                                            )
                                          }
                                        : d
                                    );
                                    setItinerary(updatedItinerary);
                                  }}
                                  className="text-right font-semibold text-platinum-50 bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-champagne-400 rounded px-2 py-1 w-24"
                                />
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeActivity(day.id, activity.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                          
                          <textarea
                            value={activity.description}
                            onChange={(e) => {
                              const updatedItinerary = itinerary.map(d => 
                                d.id === day.id 
                                  ? {
                                      ...d,
                                      activities: d.activities.map(a => 
                                        a.id === activity.id ? { ...a, description: e.target.value } : a
                                      )
                                    }
                                  : d
                              );
                              setItinerary(updatedItinerary);
                            }}
                            className="w-full text-sm text-slate-700 bg-transparent border-none resize-none focus:outline-none focus:ring-2 focus:ring-amber-400 rounded px-2 py-1"
                            rows={2}
                            placeholder="Experience description..."
                          />
                        </div>
                      ))}
                      
                      {day.activities.length === 0 && (
                        <div className="text-center py-8 text-slate-500">
                          <Plus className="w-8 h-8 mx-auto mb-2 opacity-50" />
                          <p>No experiences added yet</p>
                        </div>
                      )}
                    </div>
                  </Card>
                </motion.div>
              ))}

              {/* Add Day Button */}
              <div className="text-center">
                <Button variant="outline" onClick={addDay}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Day
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Partner Selector Modal */}
        {showPartnerSelector && (
          <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
            <div className="bg-platinum-900/95 rounded-2xl p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto border border-champagne-500/20">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-platinum-50">Select Partner</h3>
                <Button variant="ghost" size="sm" onClick={() => setShowPartnerSelector(false)}>
                  <X className="w-5 h-5" />
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {mockPartners.map((partner) => (
                  <div
                    key={partner.id}
                    className={`p-4 border-2 rounded-xl cursor-pointer transition-all ${
                      selectedPartner === partner.id 
                        ? 'border-amber-400 bg-amber-50' 
                        : 'border-slate-200 hover:border-slate-300'
                    }`}
                    onClick={() => setSelectedPartner(partner.id)}
                  >
                    <img
                      src={partner.avatar}
                      alt={partner.name}
                      className="w-full h-32 object-cover rounded-lg mb-3"
                    />
                    <h4 className="font-semibold text-platinum-50">{partner.name}</h4>
                    <p className="text-sm text-platinum-300 capitalize">{partner.type}</p>
                    <p className="text-sm text-platinum-400">{partner.location}</p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-champagne-400 font-semibold">{partner.qualityScore}/10</span>
                      <span className="text-xs text-platinum-400">{partner.responseTime}h</span>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="flex justify-end space-x-4 mt-6">
                <Button variant="outline" onClick={() => setShowPartnerSelector(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={() => addActivity(itinerary[0].id)}
                  disabled={!selectedPartner}
                >
                  Add Experience
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}