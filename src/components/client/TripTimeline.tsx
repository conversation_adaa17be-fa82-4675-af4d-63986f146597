import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Pause, ChevronLeft, ChevronRight, MessageSquare, Calendar, MapPin, DollarSign } from 'lucide-react';
import { Trip, ItineraryDay } from '../../types';
import { formatCurrency, formatDate } from '../../lib/utils';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { MagazineActivityCard, AccommodationGalleryCard } from '../ui/LuxuryCards';

interface TripTimelineProps {
  trip: Trip;
  onAskQuestion: (dayId: string, context: string) => void;
}

export function TripTimeline({ trip, onAskQuestion }: TripTimelineProps) {
  const [currentDay, setCurrentDay] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  React.useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setCurrentDay((prev) => (prev + 1) % trip.itinerary.length);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, trip.itinerary.length]);

  const currentItinerary = trip.itinerary[currentDay];

  return (
    <div className="space-y-8">
      {/* Trip Header */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-slate-900 to-amber-900">
        <div className="absolute inset-0 bg-black/40" />
        <img
          src={trip.images[0]}
          alt={trip.title}
          className="w-full h-96 object-cover"
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-5xl font-serif font-bold mb-4"
            >
              {trip.title}
            </motion.h1>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="flex items-center justify-center space-x-8 text-lg"
            >
              <div className="flex items-center space-x-2">
                <MapPin className="w-5 h-5 text-amber-400" />
                <span>{trip.destination}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-amber-400" />
                <span>{formatDate(trip.startDate)} - {formatDate(trip.endDate)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <DollarSign className="w-5 h-5 text-amber-400" />
                <span>{formatCurrency(trip.totalCost)}</span>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Timeline Controls */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-serif font-bold text-platinum-50">Day-by-Day Timeline</h2>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDay(Math.max(0, currentDay - 1))}
              disabled={currentDay === 0}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            
            <Button
              variant={isPlaying ? "secondary" : "primary"}
              size="sm"
              onClick={() => setIsPlaying(!isPlaying)}
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              {isPlaying ? 'Pause' : 'Play'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDay(Math.min(trip.itinerary.length - 1, currentDay + 1))}
              disabled={currentDay === trip.itinerary.length - 1}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Day Indicators */}
        <div className="flex justify-center space-x-2 mb-8">
          {trip.itinerary.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentDay(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentDay ? 'bg-amber-400 scale-125' : 'bg-slate-300 hover:bg-slate-400'
              }`}
            />
          ))}
        </div>

        {/* Current Day Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentDay}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Day Header */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-3xl font-serif font-bold text-platinum-50">
                  Day {currentItinerary.day}: {currentItinerary.title}
                </h3>
                <p className="text-lg text-platinum-300 mt-2">{formatDate(currentItinerary.date)}</p>
                <p className="text-platinum-300 mt-2">{currentItinerary.description}</p>
              </div>
              <Button
                variant="outline"
                onClick={() => onAskQuestion(currentItinerary.id, `Day ${currentItinerary.day}: ${currentItinerary.title}`)}
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Ask AI
              </Button>
            </div>

            {/* Day Images */}
            {currentItinerary.images.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {currentItinerary.images.map((image, index) => (
                  <motion.img
                    key={index}
                    src={image}
                    alt={`Day ${currentItinerary.day} - Image ${index + 1}`}
                    className="w-full h-64 object-cover rounded-2xl"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  />
                ))}
              </div>
            )}

            {/* Activities - Using Magazine-Style Cards */}
            <div className="space-y-6">
              <h4 className="text-xl font-semibold text-platinum-50">Activities</h4>
              {currentItinerary.activities.map((activity, index) => (
                <MagazineActivityCard
                  key={activity.id}
                  activity={activity}
                  layout={index % 3 === 0 ? 'full' : index % 2 === 0 ? 'left' : 'right'}
                  onSelect={(selectedActivity) => {
                    console.log('Selected activity:', selectedActivity.title);
                  }}
                  onAskQuestion={(activityId, context) => {
                    onAskQuestion(currentItinerary.id, context);
                  }}
                />
              ))}
            </div>

            {/* Accommodation - Using Gallery-Style Card */}
            {currentItinerary.accommodation && (
              <div>
                <h4 className="text-xl font-semibold text-platinum-50 mb-6">Accommodation</h4>
                <div className="max-w-md">
                  <AccommodationGalleryCard
                    accommodation={currentItinerary.accommodation}
                    onSelect={(selectedAccommodation) => {
                      console.log('Selected accommodation:', selectedAccommodation.name);
                    }}
                  />
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </Card>
    </div>
  );
}