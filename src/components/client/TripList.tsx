import React from 'react';
import { motion } from 'framer-motion';
import { Plus, MapPin, Calendar, Users } from 'lucide-react';
import { useTrips } from '../../hooks/api/useTrips';
import { useAuthState } from '../../hooks/useAuth';
import { LoadingSpinner, LoadingSkeleton } from '../ui/Loading';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { formatCurrency, formatDate } from '../../lib/utils';
import { luxuryAnimations } from '../../lib/animations';

export function TripList() {
  const { user } = useAuthState();
  const { data: trips, isLoading, error, refetch } = useTrips(user?.id);

  // Loading state with luxury spinner
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <LoadingSkeleton variant="text" lines={1} className="w-48 h-8" />
          <LoadingSkeleton variant="text" lines={1} className="w-32 h-10" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <LoadingSkeleton
              key={index}
              variant="card"
              className="h-80"
            />
          ))}
        </div>
      </div>
    );
  }

  // Error state with retry option
  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <div className="bg-red-500/10 border border-red-500/20 rounded-2xl p-8 max-w-md mx-auto">
          <h3 className="text-xl font-semibold text-red-400 mb-4">
            Unable to Load Trips
          </h3>
          <p className="text-platinum-300 mb-6">
            {error.message || 'There was an error loading your luxury travel experiences.'}
          </p>
          <Button
            onClick={() => refetch()}
            variant="outline"
            className="border-red-500/30 text-red-400 hover:bg-red-500/10"
          >
            Try Again
          </Button>
        </div>
      </motion.div>
    );
  }

  // Empty state
  if (!trips || trips.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <div className="bg-platinum-800/30 rounded-2xl p-12 max-w-lg mx-auto">
          <MapPin className="w-16 h-16 text-champagne-400 mx-auto mb-6" />
          <h3 className="text-2xl font-serif text-platinum-50 mb-4">
            No Trips Yet
          </h3>
          <p className="text-platinum-300 mb-8">
            Start planning your next luxury adventure in Morocco
          </p>
          <Button size="lg">
            <Plus className="w-5 h-5 mr-2" />
            Plan New Trip
          </Button>
        </div>
      </motion.div>
    );
  }

  // Success state with trips
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-serif text-platinum-50">
          Your Luxury Experiences
        </h2>
        <Button>
          <Plus className="w-5 h-5 mr-2" />
          New Trip
        </Button>
      </div>

      {/* Trips Grid */}
      <motion.div
        variants={luxuryAnimations.staggerContainer}
        initial="initial"
        animate="animate"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {trips.map((trip) => (
          <motion.div
            key={trip.id}
            variants={luxuryAnimations.staggerItem}
            whileHover={luxuryAnimations.cardHover}
            className="group"
          >
            <Card className="h-full overflow-hidden">
              {/* Trip Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={trip.heroImage}
                  alt={trip.title}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                
                {/* Status Badge */}
                <div className="absolute top-4 right-4">
                  <span className={`
                    px-3 py-1 rounded-full text-xs font-medium
                    ${trip.status === 'confirmed' 
                      ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                      : trip.status === 'pending'
                      ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                      : 'bg-champagne-500/20 text-champagne-400 border border-champagne-500/30'
                    }
                  `}>
                    {trip.status.charAt(0).toUpperCase() + trip.status.slice(1)}
                  </span>
                </div>
              </div>

              {/* Trip Content */}
              <div className="p-6 space-y-4">
                <div>
                  <h3 className="text-xl font-serif text-platinum-50 mb-2 line-clamp-2">
                    {trip.title}
                  </h3>
                  <p className="text-platinum-300 text-sm line-clamp-2">
                    {trip.description}
                  </p>
                </div>

                {/* Trip Details */}
                <div className="space-y-2 text-sm">
                  <div className="flex items-center text-platinum-400">
                    <MapPin className="w-4 h-4 mr-2" />
                    {trip.destinations.join(', ')}
                  </div>
                  
                  <div className="flex items-center text-platinum-400">
                    <Calendar className="w-4 h-4 mr-2" />
                    {formatDate(trip.startDate)} - {formatDate(trip.endDate)}
                  </div>
                  
                  <div className="flex items-center text-platinum-400">
                    <Users className="w-4 h-4 mr-2" />
                    {trip.guestCount} guests
                  </div>
                </div>

                {/* Price */}
                <div className="pt-4 border-t border-platinum-700/30">
                  <div className="flex items-center justify-between">
                    <span className="text-platinum-400 text-sm">Total Investment</span>
                    <span className="text-champagne-400 font-semibold text-lg">
                      {formatCurrency(trip.totalPrice)}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  );
}