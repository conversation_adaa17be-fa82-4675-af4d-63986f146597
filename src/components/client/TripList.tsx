import React from 'react';
import { motion } from 'framer-motion';
import { Plus, MapPin, Calendar, Users } from 'lucide-react';
import { useTrips } from '../../hooks/api/useTrips';
import { useAuthState } from '../../hooks/useAuth';
import { LoadingSpinner, LoadingSkeleton } from '../ui/Loading';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { LuxuryTripCard, AdaptiveGrid } from '../ui/LuxuryCards';
import { formatCurrency, formatDate } from '../../lib/utils';
import { luxuryAnimations } from '../../lib/animations';

export function TripList() {
  const { user } = useAuthState();
  const { data: trips, isLoading, error, refetch } = useTrips(user?.id);

  // Loading state with luxury spinner
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <LoadingSkeleton variant="text" lines={1} className="w-48 h-8" />
          <LoadingSkeleton variant="text" lines={1} className="w-32 h-10" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <LoadingSkeleton
              key={index}
              variant="card"
              className="h-80"
            />
          ))}
        </div>
      </div>
    );
  }

  // Error state with retry option
  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <div className="bg-red-500/10 border border-red-500/20 rounded-2xl p-8 max-w-md mx-auto">
          <h3 className="text-xl font-semibold text-red-400 mb-4">
            Unable to Load Trips
          </h3>
          <p className="text-platinum-300 mb-6">
            {error.message || 'There was an error loading your luxury travel experiences.'}
          </p>
          <Button
            onClick={() => refetch()}
            variant="outline"
            className="border-red-500/30 text-red-400 hover:bg-red-500/10"
          >
            Try Again
          </Button>
        </div>
      </motion.div>
    );
  }

  // Empty state
  if (!trips || trips.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <div className="bg-platinum-800/30 rounded-2xl p-12 max-w-lg mx-auto">
          <MapPin className="w-16 h-16 text-champagne-400 mx-auto mb-6" />
          <h3 className="text-2xl font-serif text-platinum-50 mb-4">
            No Trips Yet
          </h3>
          <p className="text-platinum-300 mb-8">
            Start planning your next luxury adventure in Morocco
          </p>
          <Button size="lg">
            <Plus className="w-5 h-5 mr-2" />
            Plan New Trip
          </Button>
        </div>
      </motion.div>
    );
  }

  // Success state with trips
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-serif text-platinum-50">
          Your Luxury Experiences
        </h2>
        <Button>
          <Plus className="w-5 h-5 mr-2" />
          New Trip
        </Button>
      </div>

      {/* Trips Grid - Using New Luxury Cards */}
      <AdaptiveGrid minItemWidth={380} gap="lg">
        {trips.map((trip, index) => (
          <LuxuryTripCard
            key={trip.id}
            trip={trip}
            variant={index === 0 ? 'featured' : index % 4 === 0 ? 'compact' : 'standard'}
            onSelect={(selectedTrip) => {
              console.log('Selected trip:', selectedTrip.title);
              // Add navigation logic here
            }}
            onFavorite={(tripId) => {
              console.log('Favorited trip:', tripId);
              // Add favorite logic here
            }}
            onShare={(tripId) => {
              console.log('Shared trip:', tripId);
              // Add share logic here
            }}
          />
        ))}
      </AdaptiveGrid>
    </motion.div>
  );
}