import { useState } from 'react';
import { Calendar, MapPin, Clock, Star, MessageSquare, ArrowRight, Plane, Hotel, Utensils } from 'lucide-react';
import { useAuthState } from '../../hooks/useAuth';
import { useTrips, useTripStats, useFeaturedDestinations, useCreateTripInquiry } from '../../hooks/api/useTrips';
import { formatCurrency, formatDate } from '../../lib/utils';
import { Button } from '../ui/Button';
import { InteractiveCard } from '../ui/InteractiveCard';
import { SectionTransition, StaggerContainer, StaggerItem } from '../ui/PageTransition';
import { LoadingSpinner, LoadingSkeleton } from '../ui/Loading';
import { TripTimeline } from './TripTimeline';
import { VVIPTripExperience } from './VVIPTripExperience';
import { AIChat } from './AIChat';
import { Trip } from '../../types';
import { mockTrips } from '../../data/mockData';

interface TripStats {
  activeTrips: number;
  upcomingTrips: number;
  totalDestinations: number;
  totalExperiences: number;
}

interface FeaturedDestination {
  title: string;
  description: string;
  image: string;
  price: string;
}

export function ClientDashboard() {
  const { user } = useAuthState();
  const [selectedTrip, setSelectedTrip] = useState<string | null>(null);
  const [aiChatOpen, setAiChatOpen] = useState(false);
  const [chatContext, setChatContext] = useState('');

  // API hooks for real data
  const { data: userTrips, isLoading: tripsLoading, error: tripsError } = useTrips(user?.id);
  const { data: tripStats, isLoading: statsLoading } = useTripStats(user?.id || '');
  const { data: featuredDestinations, isLoading: destinationsLoading } = useFeaturedDestinations();
  const createInquiryMutation = useCreateTripInquiry();

  // Ensure we have safe defaults with proper typing
  // Use mock data as fallback if API data is not available
  const safeUserTrips: Trip[] = userTrips && userTrips.length > 0 ? userTrips : mockTrips.filter(trip => trip.clientId === user?.id);
  const safeTripStats: TripStats = tripStats || {
    activeTrips: safeUserTrips.filter(t => t.status === 'confirmed' || t.status === 'in-progress').length,
    upcomingTrips: safeUserTrips.filter(t => t.status === 'proposed' || t.status === 'pending').length,
    totalDestinations: new Set(safeUserTrips.flatMap(t => t.destinations || [t.destination])).size,
    totalExperiences: safeUserTrips.reduce((acc, trip) => acc + (trip.itinerary?.reduce((dayAcc, day) => dayAcc + day.activities.length, 0) || 0), 0)
  };
  const safeFeaturedDestinations: FeaturedDestination[] = featuredDestinations || [];



  const handleAskQuestion = (dayId: string, context: string) => {
    setChatContext(context);
    setAiChatOpen(true);
    
    // Create trip inquiry for AI context
    if (user?.id) {
      createInquiryMutation.mutate({
        clientId: user.id,
        context: `${context} - Question about: ${dayId}`,
        preferences: { source: 'dashboard_ai_chat' }
      });
    }
  };

  const selectedTripData = safeUserTrips.find((trip: Trip) => trip.id === selectedTrip);

  // Show loading state for initial data fetch
  if (tripsLoading || statsLoading) {
    return (
      <div className="min-h-screen bg-luxury-gradient-light dark:bg-luxury-gradient transition-colors duration-300">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <LoadingSkeleton variant="text" lines={2} className="mb-12 max-w-2xl" />
          
          {/* Stats skeleton */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-12">
            {Array.from({ length: 4 }).map((_, i) => (
              <LoadingSkeleton key={i} variant="card" className="h-24" />
            ))}
          </div>
          
          {/* Trips skeleton */}
          <div className="space-y-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <LoadingSkeleton key={i} variant="card" className="h-64" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (tripsError) {
    return (
      <div className="min-h-screen bg-luxury-gradient-light dark:bg-luxury-gradient flex items-center justify-center transition-colors duration-300">
        <div className="text-center max-w-md">
          <div className="bg-red-500/10 border border-red-500/20 rounded-2xl p-8">
            <h3 className="text-xl font-semibold text-red-400 mb-4">
              Unable to Load Dashboard
            </h3>
            <p className="text-platinum-300 mb-6">
              We're having trouble loading your luxury experiences. Please try again.
            </p>
            <Button onClick={() => window.location.reload()}>
              <LoadingSpinner size="sm" className="mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (selectedTrip && selectedTripData) {
    return (
      <>
        <VVIPTripExperience
          trip={selectedTripData}
          onBack={() => setSelectedTrip(null)}
          onAskQuestion={handleAskQuestion}
        />
        <AIChat
          isOpen={aiChatOpen}
          onClose={() => setAiChatOpen(false)}
          context={chatContext}
        />
      </>
    );
  }

  return (
    <div className="min-h-screen bg-luxury-gradient-light dark:bg-luxury-gradient transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Welcome Section */}
        <SectionTransition>
          <div className="mb-12 md:mb-16">
            <h1 className="text-display-sm md:text-display-lg font-serif font-bold text-slate-900 dark:text-platinum-50 mb-4 md:mb-6 text-balance">
              Welcome back, {user?.name}
            </h1>
            <p className="text-body-lg md:text-body-xl text-slate-700 dark:text-platinum-300 max-w-2xl">
              Your curated luxury experiences await discovery
            </p>
          </div>
        </SectionTransition>

        {/* Quick Stats */}
        <SectionTransition delay={0.2}>
          <StaggerContainer className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-8 md:mb-12">
            {[
              { 
                icon: Plane, 
                label: 'Active Trips', 
                value: safeTripStats.activeTrips.toString(), 
                color: 'bg-gradient-to-br from-blue-500 to-blue-600',
                accent: 'text-blue-400'
              },
              { 
                icon: Calendar, 
                label: 'Upcoming', 
                value: safeTripStats.upcomingTrips.toString(), 
                color: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
                accent: 'text-emerald-400'
              },
              { 
                icon: MapPin, 
                label: 'Destinations', 
                value: safeTripStats.totalDestinations.toString(), 
                color: 'bg-gradient-to-br from-purple-500 to-purple-600',
                accent: 'text-purple-400'
              },
              { 
                icon: Star, 
                label: 'Experiences', 
                value: safeTripStats.totalExperiences.toString(), 
                color: 'bg-champagne-gradient',
                accent: 'text-champagne-400'
              }
            ].map((stat) => (
              <StaggerItem key={stat.label}>
                <InteractiveCard hover glow className="p-4 md:p-6 bg-platinum-900/60 border border-platinum-700/30 backdrop-blur-sm">
                  <div className="flex flex-col space-y-3 md:space-y-4">
                    <div className="flex items-center justify-between">
                      <div className={`w-10 h-10 md:w-12 md:h-12 rounded-xl md:rounded-2xl ${stat.color} flex items-center justify-center shadow-lg`}>
                        <stat.icon className="w-5 h-5 md:w-6 md:h-6 text-white" />
                      </div>
                      <div className={`w-2 h-2 rounded-full ${stat.color} opacity-60`} />
                    </div>
                    <div>
                      <p className="text-caption-sm md:text-caption-md text-platinum-400 uppercase tracking-wider font-medium">{stat.label}</p>
                      <p className={`text-heading-xl md:text-display-sm font-bold mt-1 md:mt-2 tabular-nums ${stat.accent}`}>
                        {stat.value}
                      </p>
                    </div>
                  </div>
                </InteractiveCard>
              </StaggerItem>
            ))}
          </StaggerContainer>
        </SectionTransition>

        {/* Current Proposals */}
        <SectionTransition delay={0.4}>
          <section className="mb-12 md:mb-16">
            <div className="mb-6 md:mb-8">
              <h2 className="text-display-sm md:text-display-md font-serif font-bold text-slate-900 dark:text-platinum-50 mb-2 md:mb-3">Current Proposals</h2>
              <p className="text-body-md md:text-body-lg text-slate-600 dark:text-platinum-400">Exclusive itineraries crafted for your preferences</p>
            </div>
            <StaggerContainer className="space-y-4 md:space-y-6">
              {safeUserTrips.map((trip) => (
                <StaggerItem key={trip.id}>
                  <InteractiveCard hover tilt glow className="overflow-hidden">
                    <div className="flex flex-col md:flex-row">
                      <div className="md:w-1/3">
                        <img
                          src={trip.images[0]}
                          alt={trip.title}
                          className="w-full h-48 md:h-64 lg:h-full object-cover"
                        />
                      </div>
                      <div className="md:w-2/3 p-4 md:p-6 lg:p-8">
                        <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-6 space-y-4 md:space-y-0">
                          <div className="flex-1">
                            <h3 className="text-heading-lg md:text-heading-xl font-serif font-bold text-platinum-50 mb-3 md:mb-4 text-balance leading-tight">
                              {trip.title}
                            </h3>
                            <div className="flex items-center text-body-md md:text-body-lg text-platinum-300 mb-4 md:mb-6">
                              <div className="w-5 h-5 md:w-6 md:h-6 rounded-full bg-champagne-500/20 flex items-center justify-center mr-3 flex-shrink-0">
                                <MapPin className="w-3 h-3 md:w-4 md:h-4 text-champagne-400" />
                              </div>
                              <span className="font-medium">{trip.destination}</span>
                            </div>
                          </div>
                          <div className="text-left md:text-right space-y-3 md:space-y-4 md:ml-6">
                            <div className="bg-champagne-500/10 border border-champagne-500/20 rounded-xl p-3 md:p-4">
                              <p className="text-caption-sm text-champagne-400 uppercase tracking-wider font-medium mb-1">Total Investment</p>
                              <p className="text-price-md md:text-price-lg font-serif font-bold text-champagne-400 tabular-nums">
                                {formatCurrency(trip.totalCost, { style: 'elegant' })}
                              </p>
                            </div>
                            <span className={`inline-flex items-center px-3 py-2 md:px-4 md:py-2 rounded-full text-caption-md md:text-caption-lg font-semibold uppercase tracking-wider shadow-sm ${
                              trip.status === 'proposed' ? 'bg-champagne-500/20 text-champagne-400 border border-champagne-500/30' :
                              trip.status === 'confirmed' ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30' :
                              'bg-platinum-600/20 text-platinum-300 border border-platinum-600/30'
                            }`}>
                              {trip.status}
                            </span>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6 md:mb-8">
                          <div className="bg-platinum-800/30 rounded-xl p-4 border border-platinum-700/20">
                            <div className="text-caption-sm md:text-caption-md text-platinum-400 uppercase tracking-wider font-medium mb-2">Departure</div>
                            <div className="flex items-center text-platinum-200">
                              <div className="w-8 h-8 rounded-lg bg-champagne-500/20 flex items-center justify-center mr-3 flex-shrink-0">
                                <Calendar className="w-4 h-4 text-champagne-400" />
                              </div>
                              <span className="text-body-md md:text-body-lg font-semibold">{formatDate(trip.startDate)}</span>
                            </div>
                          </div>
                          <div className="bg-platinum-800/30 rounded-xl p-4 border border-platinum-700/20">
                            <div className="text-caption-sm md:text-caption-md text-platinum-400 uppercase tracking-wider font-medium mb-2">Duration</div>
                            <div className="flex items-center text-platinum-200">
                              <div className="w-8 h-8 rounded-lg bg-champagne-500/20 flex items-center justify-center mr-3 flex-shrink-0">
                                <Clock className="w-4 h-4 text-champagne-400" />
                              </div>
                              <span className="text-body-md md:text-body-lg font-semibold">{trip.itinerary.length} days</span>
                            </div>
                          </div>
                        </div>

                        {/* Trip Highlights */}
                        <div className="border-t border-platinum-700/30 pt-6 md:pt-8 mb-6 md:mb-8">
                          <div className="text-caption-sm md:text-caption-md text-platinum-400 uppercase tracking-wider font-medium mb-4 md:mb-6">Included Experiences</div>
                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 md:gap-4">
                            <div className="flex items-center text-platinum-200 bg-gradient-to-r from-platinum-800/60 to-platinum-800/40 px-3 py-3 md:px-4 md:py-4 rounded-xl border border-platinum-700/20 backdrop-blur-sm">
                              <div className="w-8 h-8 rounded-lg bg-champagne-500/20 flex items-center justify-center mr-3 flex-shrink-0">
                                <Hotel className="w-4 h-4 text-champagne-400" />
                              </div>
                              <span className="text-body-sm md:text-body-md font-medium">Luxury Suites</span>
                            </div>
                            <div className="flex items-center text-platinum-200 bg-gradient-to-r from-platinum-800/60 to-platinum-800/40 px-3 py-3 md:px-4 md:py-4 rounded-xl border border-platinum-700/20 backdrop-blur-sm">
                              <div className="w-8 h-8 rounded-lg bg-champagne-500/20 flex items-center justify-center mr-3 flex-shrink-0">
                                <Utensils className="w-4 h-4 text-champagne-400" />
                              </div>
                              <span className="text-body-sm md:text-body-md font-medium">Michelin Dining</span>
                            </div>
                            <div className="flex items-center text-platinum-200 bg-gradient-to-r from-platinum-800/60 to-platinum-800/40 px-3 py-3 md:px-4 md:py-4 rounded-xl border border-platinum-700/20 backdrop-blur-sm">
                              <div className="w-8 h-8 rounded-lg bg-champagne-500/20 flex items-center justify-center mr-3 flex-shrink-0">
                                <Star className="w-4 h-4 text-champagne-400" />
                              </div>
                              <span className="text-body-sm md:text-body-md font-medium">VIP Access</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                          <Button
                            onClick={() => setSelectedTrip(trip.id)}
                            className="flex items-center justify-center space-x-2 w-full sm:flex-1 min-h-[48px] font-semibold"
                            magnetic
                            glow
                          >
                            <span>Review Itinerary</span>
                            <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => handleAskQuestion(trip.id, `${trip.title} - ${trip.destination}`)}
                            className="flex items-center justify-center space-x-2 w-full sm:w-auto min-h-[48px] font-semibold border-champagne-500/30 hover:border-champagne-400/50 hover:bg-champagne-500/10"
                          >
                            <MessageSquare className="w-4 h-4" />
                            <span>Ask AI Concierge</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </InteractiveCard>
                </StaggerItem>
              ))}
            </StaggerContainer>
          </section>
        </SectionTransition>

        {/* Featured Destinations */}
        <SectionTransition delay={0.6}>
          <section>
            <div className="mb-8 md:mb-10">
              <h2 className="text-display-sm md:text-display-md font-serif font-bold text-platinum-900 dark:text-platinum-50 mb-2 md:mb-3">Discover More Destinations</h2>
              <p className="text-body-md md:text-body-lg text-platinum-600 dark:text-platinum-400">Handpicked experiences from around the world</p>
            </div>
            {destinationsLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                {Array.from({ length: 3 }).map((_, i) => (
                  <LoadingSkeleton key={i} variant="card" className="h-80" />
                ))}
              </div>
            ) : (
              <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                {safeFeaturedDestinations.map((destination: any) => (
                <StaggerItem key={destination.title}>
                  <InteractiveCard hover tilt glow className="overflow-hidden h-full bg-platinum-900/60 border border-platinum-700/30 backdrop-blur-sm">
                    <div className="relative">
                      <img
                        src={destination.image}
                        alt={destination.title}
                        className="w-full h-48 md:h-56 object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-platinum-900/80 via-transparent to-transparent" />
                      <div className="absolute bottom-4 left-4 right-4">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-caption-sm font-semibold uppercase tracking-wider bg-champagne-500/20 text-champagne-400 border border-champagne-500/30 backdrop-blur-sm">
                          Featured
                        </span>
                      </div>
                    </div>
                    <div className="p-6 md:p-8">
                      <h3 className="text-heading-md md:text-heading-lg font-serif font-bold text-platinum-50 mb-3 md:mb-4 text-balance leading-tight">
                        {destination.title}
                      </h3>
                      <p className="text-body-md md:text-body-lg text-platinum-300 mb-6 md:mb-8 leading-relaxed">{destination.description}</p>
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-end space-y-4 sm:space-y-0">
                        <div>
                          <p className="text-caption-sm text-platinum-400 uppercase tracking-wider font-medium mb-1">Starting from</p>
                          <span className="text-price-md md:text-price-lg font-serif font-bold text-champagne-400 tabular-nums">{destination.price}</span>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full sm:w-auto min-h-[44px] font-semibold border-champagne-500/30 hover:border-champagne-400/50 hover:bg-champagne-500/10"
                        >
                          Explore Destination
                        </Button>
                      </div>
                    </div>
                  </InteractiveCard>
                </StaggerItem>
              ))}
            </StaggerContainer>
            )}
          </section>
        </SectionTransition>
      </div>

      <AIChat
        isOpen={aiChatOpen}
        onClose={() => setAiChatOpen(false)}
        context={chatContext}
      />
    </div>
  );
}