import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Star, 
  MapPin, 
  Users, 
  Wifi, 
  Car, 
  Coffee, 
  Waves,
  Sparkles,
  Heart,
  Share2,
  MessageSquare,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { formatCurrency } from '../../lib/utils';
import { Button } from '../ui/Button';
import { InteractiveCard } from '../ui/InteractiveCard';

interface AccommodationOption {
  id: string;
  name: string;
  type: string;
  cost: number;
  images: string[];
  amenities: string[];
  partnerName: string;
  description: string;
  rating: number;
  location: string;
  rooms: number;
  maxGuests: number;
  highlights: string[];
  detailedDescription: string;
}

interface AccommodationModalProps {
  isOpen: boolean;
  onClose: () => void;
  accommodation: AccommodationOption;
  onSelect: (id: string) => void;
  isSelected: boolean;
  onAskQuestion: (context: string) => void;
}

export function AccommodationModal({ 
  isOpen, 
  onClose, 
  accommodation, 
  onSelect, 
  isSelected,
  onAskQuestion 
}: AccommodationModalProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showAllImages, setShowAllImages] = useState(false);

  if (!isOpen) return null;

  const amenityIcons: { [key: string]: React.ReactNode } = {
    'Private Pool': <Waves className="w-4 h-4" />,
    'Personal Butler': <Users className="w-4 h-4" />,
    'Rooftop Terrace': <Sparkles className="w-4 h-4" />,
    'Spa Access': <Heart className="w-4 h-4" />,
    'Private Chef': <Coffee className="w-4 h-4" />,
    'Garden View': <Sparkles className="w-4 h-4" />,
    'Butler Service': <Users className="w-4 h-4" />,
    'Private Terrace': <Sparkles className="w-4 h-4" />,
    'Authentic Design': <Star className="w-4 h-4" />,
    'Rooftop Pool': <Waves className="w-4 h-4" />,
    'Concierge': <Users className="w-4 h-4" />,
    'Traditional Hammam': <Sparkles className="w-4 h-4" />,
    'WiFi': <Wifi className="w-4 h-4" />,
    'Airport Transfer': <Car className="w-4 h-4" />
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white dark:bg-platinum-900 rounded-3xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative">
            {/* Hero Image */}
            <div className="relative h-80">
              <img
                src={accommodation.images[currentImageIndex]}
                alt={accommodation.name}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30" />
              
              {/* Navigation */}
              <div className="absolute top-6 right-6 flex space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                >
                  <Heart className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                >
                  <Share2 className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {/* Image Navigation */}
              {accommodation.images.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentImageIndex(prev => prev === 0 ? accommodation.images.length - 1 : prev - 1)}
                    className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentImageIndex(prev => prev === accommodation.images.length - 1 ? 0 : prev + 1)}
                    className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </>
              )}

              {/* Property Info Overlay */}
              <div className="absolute bottom-6 left-6 right-6">
                <div className="bg-black/40 backdrop-blur-md rounded-2xl p-6 text-white">
                  <div className="flex items-start justify-between">
                    <div>
                      <h2 className="text-3xl font-serif font-bold mb-2">{accommodation.name}</h2>
                      <p className="text-champagne-300 font-medium mb-1">{accommodation.partnerName}</p>
                      <div className="flex items-center space-x-4 text-white/80">
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          {accommodation.location}
                        </div>
                        <div className="flex items-center">
                          <Star className="w-4 h-4 mr-1 fill-current text-champagne-400" />
                          {accommodation.rating}/5
                        </div>
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          Up to {accommodation.maxGuests} guests
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-serif font-bold text-champagne-400">
                        {formatCurrency(accommodation.cost)}
                      </div>
                      <div className="text-white/70">per night</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-8 max-h-[50vh] overflow-y-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Description */}
                <div>
                  <h3 className="text-xl font-serif font-bold text-slate-900 dark:text-platinum-50 mb-3">
                    About This Property
                  </h3>
                  <p className="text-slate-700 dark:text-platinum-300 leading-relaxed">
                    {accommodation.detailedDescription || accommodation.description}
                  </p>
                </div>

                {/* Highlights */}
                {accommodation.highlights && (
                  <div>
                    <h3 className="text-xl font-serif font-bold text-slate-900 dark:text-platinum-50 mb-3">
                      Property Highlights
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {accommodation.highlights.map((highlight, index) => (
                        <div key={index} className="flex items-center text-slate-700 dark:text-platinum-300">
                          <div className="w-2 h-2 bg-champagne-500 rounded-full mr-3"></div>
                          {highlight}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Masonry Image Gallery */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-serif font-bold text-slate-900 dark:text-platinum-50">
                      Property Gallery
                    </h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowAllImages(!showAllImages)}
                      className="text-champagne-600 dark:text-champagne-400"
                    >
                      {showAllImages ? 'Show Less' : `View All ${accommodation.images.length} Photos`}
                    </Button>
                  </div>
                  
                  <div className={`columns-2 md:columns-3 gap-4 space-y-4 ${showAllImages ? '' : 'max-h-64 overflow-hidden'}`}>
                    {accommodation.images.map((image, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="break-inside-avoid mb-4 cursor-pointer"
                        onClick={() => setCurrentImageIndex(index)}
                      >
                        <img
                          src={image}
                          alt={`${accommodation.name} - Image ${index + 1}`}
                          className="w-full rounded-lg hover:shadow-lg transition-shadow duration-300"
                        />
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Amenities */}
                <div>
                  <h3 className="text-xl font-serif font-bold text-slate-900 dark:text-platinum-50 mb-4">
                    Amenities & Services
                  </h3>
                  <div className="space-y-3">
                    {accommodation.amenities.map((amenity, index) => (
                      <div key={index} className="flex items-center text-slate-700 dark:text-platinum-300">
                        <div className="w-8 h-8 bg-champagne-100 dark:bg-champagne-900/20 rounded-lg flex items-center justify-center mr-3">
                          {amenityIcons[amenity] || <Star className="w-4 h-4" />}
                        </div>
                        {amenity}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Actions */}
                <div className="space-y-3">
                  <Button
                    onClick={() => onSelect(accommodation.id)}
                    className={`w-full ${
                      isSelected 
                        ? 'bg-green-600 hover:bg-green-700 text-white' 
                        : 'bg-champagne-gradient text-platinum-900'
                    }`}
                  >
                    {isSelected ? '✓ Selected' : 'Select This Property'}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    onClick={() => onAskQuestion(`${accommodation.name} - Property Details`)}
                    className="w-full text-slate-600 dark:text-platinum-400 hover:text-champagne-500"
                  >
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Ask Our Concierge
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
