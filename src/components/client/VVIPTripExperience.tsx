import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import { 
  ArrowLeft, 
  Calendar, 
  MapPin, 
  Users, 
  Star, 
  MessageSquare, 
  Share2, 
  Heart,
  Play,
  ChevronLeft,
  ChevronRight,
  Clock,
  Camera,
  Sparkles
} from 'lucide-react';
import { Trip, ItineraryDay } from '../../types';
import { formatCurrency, formatDate } from '../../lib/utils';
import { Button } from '../ui/Button';
import { InteractiveCard } from '../ui/InteractiveCard';

interface VVIPTripExperienceProps {
  trip: Trip;
  onBack: () => void;
  onAskQuestion: (dayId: string, context: string) => void;
}

export function VVIPTripExperience({ trip, onBack, onAskQuestion }: VVIPTripExperienceProps) {
  const [currentDay, setCurrentDay] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentActivityIndex, setCurrentActivityIndex] = useState(0);
  const [showQuoteActions, setShowQuoteActions] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<string | null>(null);
  const [isEditingQuote, setIsEditingQuote] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [mouseMoving, setMouseMoving] = useState(false);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const heroY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const heroOpacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  const currentItinerary = trip.itinerary[currentDay];
  const allImages = trip.itinerary.flatMap(day => [
    ...day.images,
    ...(day.accommodation?.images || []),
    ...day.activities.flatMap(activity => activity.images)
  ]);

  // Mouse movement detection for pause
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    const handleMouseMove = () => {
      setMouseMoving(true);
      clearTimeout(timeout);
      timeout = setTimeout(() => setMouseMoving(false), 2000);
    };

    if (isPlaying) {
      document.addEventListener('mousemove', handleMouseMove);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        clearTimeout(timeout);
      };
    }
  }, [isPlaying]);

  // Cinematic auto-play through all images and activities
  useEffect(() => {
    if (isPlaying && !mouseMoving) {
      const interval = setInterval(() => {
        const currentDayData = trip.itinerary[currentDay];
        const totalActivities = currentDayData.activities.length;

        if (currentActivityIndex < totalActivities - 1) {
          setCurrentActivityIndex(prev => prev + 1);
        } else {
          // Move to next day
          if (currentDay < trip.itinerary.length - 1) {
            setCurrentDay(prev => prev + 1);
            setCurrentActivityIndex(0);
          } else {
            // Reset to beginning
            setCurrentDay(0);
            setCurrentActivityIndex(0);
          }
        }
      }, 3000); // 3 seconds per activity/image
      return () => clearInterval(interval);
    }
  }, [isPlaying, mouseMoving, currentDay, currentActivityIndex, trip.itinerary.length]);

  const handleBeginJourney = () => {
    setShowQuoteActions(true);
  };

  const handleAcceptQuote = () => {
    // Implementation for accepting quote
    console.log('Quote accepted for trip:', trip.id);
  };

  const handleAmendQuote = () => {
    setIsEditingQuote(true);
  };

  return (
    <div ref={containerRef} className="min-h-screen bg-luxury-gradient-light dark:bg-luxury-gradient">
      {/* Immersive Hero Section */}
      <motion.div 
        className="relative h-screen overflow-hidden"
        style={{ y: heroY, opacity: heroOpacity }}
      >
        {/* Cinematic Background with Auto-changing Images */}
        <div className="absolute inset-0">
          <AnimatePresence mode="wait">
            <motion.img
              key={isPlaying ? `${currentDay}-${currentActivityIndex}` : 'hero'}
              src={isPlaying && currentItinerary?.activities[currentActivityIndex]?.images[0]
                ? currentItinerary.activities[currentActivityIndex].images[0]
                : trip.heroImage}
              alt={trip.title}
              className="w-full h-full object-cover"
              initial={{ scale: 1.1, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              transition={{ duration: 1.5, ease: "easeOut" }}
            />
          </AnimatePresence>
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-transparent to-transparent" />

          {/* Cinematic Progress Indicator */}
          {isPlaying && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
              <div className="flex items-center space-x-2 bg-black/40 backdrop-blur-md rounded-full px-4 py-2">
                <div className="text-white/80 text-sm">
                  Day {currentDay + 1} • {currentItinerary?.activities[currentActivityIndex]?.title || 'Experience'}
                </div>
                <div className="w-24 h-1 bg-white/20 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-champagne-400"
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    transition={{ duration: 3, ease: "linear" }}
                    key={`${currentDay}-${currentActivityIndex}`}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Floating Navigation */}
        <motion.div 
          className="absolute top-8 left-8 z-20"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Button
            variant="ghost"
            onClick={onBack}
            className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Proposals
          </Button>
        </motion.div>

        {/* Trip Actions */}
        <motion.div 
          className="absolute top-8 right-8 z-20 flex space-x-3"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Button variant="ghost" size="sm" className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20">
            <Heart className="w-5 h-5" />
          </Button>
          <Button variant="ghost" size="sm" className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20">
            <Share2 className="w-5 h-5" />
          </Button>
          <Button variant="ghost" size="sm" className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20">
            <MessageSquare className="w-5 h-5" />
          </Button>
        </motion.div>

        {/* Hero Content */}
        <div className="absolute inset-0 flex items-center justify-center text-center z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="max-w-4xl px-8"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
              className="mb-6"
            >
              <span className="inline-flex items-center px-4 py-2 bg-champagne-500/20 backdrop-blur-md border border-champagne-400/30 rounded-full text-champagne-300 text-sm font-medium uppercase tracking-wider">
                <Sparkles className="w-4 h-4 mr-2" />
                {trip.status === 'proposed' ? 'Exclusive Proposal' : 'Confirmed Journey'}
              </span>
            </motion.div>

            <h1 className="text-5xl md:text-7xl font-serif font-bold text-white mb-6 leading-tight">
              {trip.title}
            </h1>
            
            <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed max-w-3xl mx-auto">
              {trip.description}
            </p>

            {/* Trip Metadata */}
            <div className="flex flex-wrap justify-center gap-8 mb-12">
              <div className="flex items-center text-white/80">
                <Calendar className="w-5 h-5 mr-3 text-champagne-400" />
                <div className="text-left">
                  <div className="text-sm opacity-75">Duration</div>
                  <div className="font-semibold">{Math.ceil((trip.endDate.getTime() - trip.startDate.getTime()) / (1000 * 60 * 60 * 24))} Days</div>
                </div>
              </div>
              
              <div className="flex items-center text-white/80">
                <MapPin className="w-5 h-5 mr-3 text-champagne-400" />
                <div className="text-left">
                  <div className="text-sm opacity-75">Destinations</div>
                  <div className="font-semibold">{trip.destinations.length} Locations</div>
                </div>
              </div>
              
              <div className="flex items-center text-white/80">
                <Users className="w-5 h-5 mr-3 text-champagne-400" />
                <div className="text-left">
                  <div className="text-sm opacity-75">Guests</div>
                  <div className="font-semibold">{trip.guestCount} People</div>
                </div>
              </div>
            </div>

            {/* Investment Display */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="mb-8"
            >
              <div className="text-white/70 text-sm uppercase tracking-wider font-medium mb-2">Total Investment</div>
              <div className="text-4xl md:text-5xl font-serif font-bold text-champagne-400">
                {formatCurrency(trip.totalPrice)}
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.1 }}
              className="flex flex-wrap justify-center gap-4"
            >
              {!showQuoteActions ? (
                <>
                  <Button
                    size="lg"
                    onClick={handleBeginJourney}
                    className="bg-champagne-gradient text-platinum-900 hover:scale-105 transition-transform"
                  >
                    Begin This Journey
                  </Button>
                  <Button
                    variant="ghost"
                    size="lg"
                    className="bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                    onClick={() => onAskQuestion('hero', `${trip.title} - General Inquiry`)}
                  >
                    <MessageSquare className="w-5 h-5 mr-2" />
                    Ask Our Concierge
                  </Button>
                </>
              ) : (
                <div className="flex flex-wrap justify-center gap-4">
                  <Button
                    size="lg"
                    onClick={handleAcceptQuote}
                    className="bg-green-600 hover:bg-green-700 text-white hover:scale-105 transition-transform"
                  >
                    Accept Proposal
                  </Button>
                  <Button
                    size="lg"
                    onClick={handleAmendQuote}
                    className="bg-champagne-gradient text-platinum-900 hover:scale-105 transition-transform"
                  >
                    Amend Proposal
                  </Button>
                  <Button
                    variant="ghost"
                    size="lg"
                    className="bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                    onClick={() => onAskQuestion('hero', `${trip.title} - Proposal Discussion`)}
                  >
                    <MessageSquare className="w-5 h-5 mr-2" />
                    Discuss Changes
                  </Button>
                </div>
              )}
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60"
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
        >
          <div className="flex flex-col items-center">
            <div className="text-sm mb-2">Explore Journey</div>
            <div className="w-px h-8 bg-white/40"></div>
          </div>
        </motion.div>
      </motion.div>

      {/* Journey Timeline Section */}
      <div className="relative bg-white/95 dark:bg-platinum-900/95 backdrop-blur-sm">
        {/* Timeline Navigation */}
        <div className="sticky top-0 z-30 bg-white/95 dark:bg-platinum-900/95 backdrop-blur-sm border-b border-champagne-500/20">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-serif font-bold text-platinum-900 dark:text-platinum-50">
                Journey Timeline
              </h2>
              
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="text-platinum-600 dark:text-platinum-400"
                >
                  <Play className={`w-4 h-4 mr-2 ${isPlaying ? 'animate-pulse' : ''}`} />
                  {isPlaying ? 'Pause' : 'Auto Play'}
                </Button>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentDay(Math.max(0, currentDay - 1))}
                    disabled={currentDay === 0}
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <span className="text-sm text-platinum-600 dark:text-platinum-400 min-w-[80px] text-center">
                    Day {currentDay + 1} of {trip.itinerary.length}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentDay(Math.min(trip.itinerary.length - 1, currentDay + 1))}
                    disabled={currentDay === trip.itinerary.length - 1}
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Day Selector */}
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {trip.itinerary.map((day, index) => (
                <motion.button
                  key={day.id}
                  onClick={() => setCurrentDay(index)}
                  className={`flex-shrink-0 px-4 py-2 rounded-xl text-sm font-medium transition-all ${
                    index === currentDay
                      ? 'bg-champagne-gradient text-platinum-900 shadow-lg'
                      : 'bg-platinum-100 dark:bg-platinum-800 text-platinum-600 dark:text-platinum-400 hover:bg-platinum-200 dark:hover:bg-platinum-700'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Day {index + 1}
                </motion.button>
              ))}
            </div>
          </div>
        </div>

        {/* Current Day Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentDay}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5 }}
            className="max-w-7xl mx-auto px-6 py-12"
          >
            {currentItinerary && (
              <>
                {/* Day Header */}
                <div className="mb-12">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-champagne-gradient rounded-full flex items-center justify-center text-platinum-900 font-bold text-lg mr-4">
                      {currentDay + 1}
                    </div>
                    <div>
                      <h3 className="text-3xl font-serif font-bold text-platinum-900 dark:text-platinum-50">
                        {currentItinerary.title}
                      </h3>
                      <p className="text-platinum-600 dark:text-platinum-400">
                        {formatDate(currentItinerary.date)}
                      </p>
                    </div>
                  </div>
                  <p className="text-lg text-platinum-700 dark:text-platinum-300 max-w-4xl">
                    {currentItinerary.description}
                  </p>
                </div>

                {/* Day Image Gallery */}
                {currentItinerary.images.length > 0 && (
                  <div className="mb-12">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {currentItinerary.images.map((image, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="relative aspect-[4/3] rounded-2xl overflow-hidden group cursor-pointer"
                        >
                          <img
                            src={image}
                            alt={`${currentItinerary.title} - Image ${index + 1}`}
                            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="w-8 h-8 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center">
                              <Camera className="w-4 h-4 text-white" />
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Accommodation Section */}
                {currentItinerary.accommodation && (
                  <div className="mb-12">
                    <h4 className="text-2xl font-serif font-bold text-platinum-900 dark:text-platinum-50 mb-6">
                      Your Sanctuary
                    </h4>
                    <InteractiveCard hover className="overflow-hidden">
                      <div className="md:flex">
                        <div className="md:w-1/2">
                          <img
                            src={currentItinerary.accommodation.images[0]}
                            alt={currentItinerary.accommodation.name}
                            className="w-full h-64 md:h-full object-cover"
                          />
                        </div>
                        <div className="md:w-1/2 p-8">
                          <div className="flex justify-between items-start mb-4">
                            <div>
                              <h5 className="text-xl font-serif font-bold text-platinum-900 dark:text-platinum-50 mb-2">
                                {currentItinerary.accommodation.name}
                              </h5>
                              <p className="text-champagne-500 dark:text-champagne-400 font-medium">
                                {currentItinerary.accommodation.partnerName}
                              </p>
                              <p className="text-platinum-600 dark:text-platinum-400 text-sm">
                                {currentItinerary.accommodation.type}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-lg font-semibold text-platinum-900 dark:text-platinum-50">
                                {formatCurrency(currentItinerary.accommodation.cost)}
                              </p>
                              <p className="text-platinum-600 dark:text-platinum-400 text-sm">per night</p>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-2 mb-4">
                            {currentItinerary.accommodation.amenities.map((amenity, index) => (
                              <span
                                key={index}
                                className="px-3 py-1 bg-champagne-500/10 text-champagne-600 dark:text-champagne-400 rounded-full text-sm font-medium"
                              >
                                {amenity}
                              </span>
                            ))}
                          </div>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onAskQuestion(currentItinerary.id, `${currentItinerary.accommodation?.name} accommodation details`)}
                            className="text-platinum-600 dark:text-platinum-400 hover:text-champagne-500"
                          >
                            <MessageSquare className="w-4 h-4 mr-2" />
                            Ask about this accommodation
                          </Button>
                        </div>
                      </div>
                    </InteractiveCard>
                  </div>
                )}

                {/* Revolutionary Masonry Activities Layout */}
                <div>
                  <h4 className="text-2xl font-serif font-bold text-slate-900 dark:text-platinum-50 mb-6">
                    Today's Experiences
                  </h4>

                  {/* Masonry Grid */}
                  <div className="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6">
                    {currentItinerary.activities.map((activity, index) => (
                      <motion.div
                        key={activity.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="break-inside-avoid mb-6"
                      >
                        <InteractiveCard
                          hover
                          className="overflow-hidden cursor-pointer group"
                          onClick={() => setSelectedActivity(selectedActivity === activity.id ? null : activity.id)}
                        >
                          {/* Activity Image */}
                          <div className="relative">
                            <img
                              src={activity.images[0]}
                              alt={activity.title}
                              className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                            {/* Time Badge */}
                            <div className="absolute top-4 left-4">
                              <div className="bg-black/40 backdrop-blur-md rounded-full px-3 py-1 text-white text-sm">
                                {activity.startTime} - {activity.endTime}
                              </div>
                            </div>

                            {/* Price Badge */}
                            <div className="absolute top-4 right-4">
                              <div className="bg-champagne-500/90 backdrop-blur-md rounded-full px-3 py-1 text-platinum-900 text-sm font-semibold">
                                {formatCurrency(activity.cost)}
                              </div>
                            </div>

                            {/* Expand Indicator */}
                            <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <div className="w-8 h-8 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center">
                                <Camera className="w-4 h-4 text-white" />
                              </div>
                            </div>
                          </div>

                          {/* Activity Content */}
                          <div className="p-6">
                            <div className="mb-3">
                              <h5 className="text-lg font-serif font-bold text-slate-900 dark:text-platinum-50 mb-1">
                                {activity.title}
                              </h5>
                              <p className="text-champagne-600 dark:text-champagne-400 font-medium text-sm">
                                {activity.partnerName}
                              </p>
                              <div className="flex items-center text-slate-600 dark:text-platinum-400 text-sm mt-1">
                                <MapPin className="w-3 h-3 mr-1" />
                                {activity.location}
                              </div>
                            </div>

                            <p className="text-slate-700 dark:text-platinum-300 text-sm leading-relaxed mb-4">
                              {activity.description}
                            </p>

                            {/* Interactive Actions */}
                            <div className="flex items-center justify-between">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onAskQuestion(currentItinerary.id, `${activity.title} at ${activity.location}`);
                                }}
                                className="text-slate-600 dark:text-platinum-400 hover:text-champagne-500 text-xs"
                              >
                                <MessageSquare className="w-3 h-3 mr-1" />
                                Ask Concierge
                              </Button>

                              <div className="flex items-center space-x-2">
                                <Star className="w-4 h-4 text-champagne-400 fill-current" />
                                <span className="text-xs text-slate-500 dark:text-platinum-500">Exclusive</span>
                              </div>
                            </div>
                          </div>

                          {/* Expanded Content */}
                          <AnimatePresence>
                            {selectedActivity === activity.id && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: 'auto', opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.3 }}
                                className="border-t border-champagne-500/20 overflow-hidden"
                              >
                                <div className="p-6">
                                  {/* Additional Images */}
                                  {activity.images.length > 1 && (
                                    <div className="grid grid-cols-2 gap-2 mb-4">
                                      {activity.images.slice(1, 5).map((image, imgIndex) => (
                                        <img
                                          key={imgIndex}
                                          src={image}
                                          alt={`${activity.title} - Image ${imgIndex + 2}`}
                                          className="w-full h-20 object-cover rounded-lg"
                                        />
                                      ))}
                                    </div>
                                  )}

                                  {/* Detailed Information */}
                                  <div className="space-y-3">
                                    <div>
                                      <h6 className="font-semibold text-slate-900 dark:text-platinum-50 text-sm mb-1">
                                        What's Included
                                      </h6>
                                      <p className="text-slate-600 dark:text-platinum-400 text-xs">
                                        Private guide, luxury transportation, exclusive access, refreshments
                                      </p>
                                    </div>

                                    <div>
                                      <h6 className="font-semibold text-slate-900 dark:text-platinum-50 text-sm mb-1">
                                        Duration
                                      </h6>
                                      <p className="text-slate-600 dark:text-platinum-400 text-xs">
                                        Approximately 3-4 hours
                                      </p>
                                    </div>

                                    <div className="flex space-x-2 pt-2">
                                      <Button
                                        size="sm"
                                        className="bg-champagne-gradient text-platinum-900 text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          // Handle customize activity
                                        }}
                                      >
                                        Customize
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="text-slate-600 dark:text-platinum-400 text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          // Handle remove activity
                                        }}
                                      >
                                        Remove
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </InteractiveCard>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Quote Editing Modal */}
      <AnimatePresence>
        {isEditingQuote && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setIsEditingQuote(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-platinum-900 rounded-2xl p-8 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-serif font-bold text-slate-900 dark:text-platinum-50">
                  Customize Your Journey
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsEditingQuote(false)}
                  className="text-slate-600 dark:text-platinum-400"
                >
                  ✕
                </Button>
              </div>

              <div className="space-y-6">
                {/* Trip Preferences */}
                <div>
                  <h4 className="font-semibold text-slate-900 dark:text-platinum-50 mb-3">
                    Travel Preferences
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-platinum-300 mb-2">
                        Guest Count
                      </label>
                      <input
                        type="number"
                        defaultValue={trip.guestCount}
                        className="w-full px-3 py-2 border border-slate-300 dark:border-platinum-600 rounded-lg bg-white dark:bg-platinum-800 text-slate-900 dark:text-platinum-50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-platinum-300 mb-2">
                        Travel Dates
                      </label>
                      <input
                        type="date"
                        defaultValue={trip.startDate.toISOString().split('T')[0]}
                        className="w-full px-3 py-2 border border-slate-300 dark:border-platinum-600 rounded-lg bg-white dark:bg-platinum-800 text-slate-900 dark:text-platinum-50"
                      />
                    </div>
                  </div>
                </div>

                {/* Special Requests */}
                <div>
                  <h4 className="font-semibold text-slate-900 dark:text-platinum-50 mb-3">
                    Special Requests
                  </h4>
                  <textarea
                    rows={4}
                    placeholder="Tell us about any special requirements, dietary restrictions, accessibility needs, or preferences..."
                    className="w-full px-3 py-2 border border-slate-300 dark:border-platinum-600 rounded-lg bg-white dark:bg-platinum-800 text-slate-900 dark:text-platinum-50 placeholder-slate-500 dark:placeholder-platinum-400"
                  />
                </div>

                {/* Budget Preferences */}
                <div>
                  <h4 className="font-semibold text-slate-900 dark:text-platinum-50 mb-3">
                    Investment Range
                  </h4>
                  <div className="flex items-center space-x-4">
                    <input
                      type="range"
                      min="50000"
                      max="500000"
                      step="10000"
                      defaultValue={trip.totalPrice}
                      className="flex-1"
                    />
                    <span className="text-champagne-600 dark:text-champagne-400 font-semibold">
                      {formatCurrency(trip.totalPrice)}
                    </span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 pt-4">
                  <Button
                    className="flex-1 bg-champagne-gradient text-platinum-900"
                    onClick={() => {
                      // Handle save changes
                      setIsEditingQuote(false);
                    }}
                  >
                    Save Changes
                  </Button>
                  <Button
                    variant="ghost"
                    className="flex-1 text-slate-600 dark:text-platinum-400"
                    onClick={() => setIsEditingQuote(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
