import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import { 
  ArrowLeft, 
  Calendar, 
  MapPin, 
  Users, 
  Star, 
  MessageSquare, 
  Share2, 
  Heart,
  Play,
  ChevronLeft,
  ChevronRight,
  Clock,
  Camera,
  Sparkles
} from 'lucide-react';
import { Trip, ItineraryDay } from '../../types';
import { formatCurrency, formatDate } from '../../lib/utils';
import { Button } from '../ui/Button';
import { InteractiveCard } from '../ui/InteractiveCard';

interface VVIPTripExperienceProps {
  trip: Trip;
  onBack: () => void;
  onAskQuestion: (dayId: string, context: string) => void;
}

export function VVIPTripExperience({ trip, onBack, onAskQuestion }: VVIPTripExperienceProps) {
  const [currentDay, setCurrentDay] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const heroY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const heroOpacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  const currentItinerary = trip.itinerary[currentDay];

  // Auto-play through days
  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setCurrentDay(prev => (prev + 1) % trip.itinerary.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, trip.itinerary.length]);

  return (
    <div ref={containerRef} className="min-h-screen bg-luxury-gradient-light dark:bg-luxury-gradient">
      {/* Immersive Hero Section */}
      <motion.div 
        className="relative h-screen overflow-hidden"
        style={{ y: heroY, opacity: heroOpacity }}
      >
        {/* Background Image with Parallax */}
        <div className="absolute inset-0">
          <motion.img
            src={trip.heroImage}
            alt={trip.title}
            className="w-full h-full object-cover"
            initial={{ scale: 1.1 }}
            animate={{ scale: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-transparent to-transparent" />
        </div>

        {/* Floating Navigation */}
        <motion.div 
          className="absolute top-8 left-8 z-20"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Button
            variant="ghost"
            onClick={onBack}
            className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Proposals
          </Button>
        </motion.div>

        {/* Trip Actions */}
        <motion.div 
          className="absolute top-8 right-8 z-20 flex space-x-3"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Button variant="ghost" size="sm" className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20">
            <Heart className="w-5 h-5" />
          </Button>
          <Button variant="ghost" size="sm" className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20">
            <Share2 className="w-5 h-5" />
          </Button>
          <Button variant="ghost" size="sm" className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20">
            <MessageSquare className="w-5 h-5" />
          </Button>
        </motion.div>

        {/* Hero Content */}
        <div className="absolute inset-0 flex items-center justify-center text-center z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="max-w-4xl px-8"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
              className="mb-6"
            >
              <span className="inline-flex items-center px-4 py-2 bg-champagne-500/20 backdrop-blur-md border border-champagne-400/30 rounded-full text-champagne-300 text-sm font-medium uppercase tracking-wider">
                <Sparkles className="w-4 h-4 mr-2" />
                {trip.status === 'proposed' ? 'Exclusive Proposal' : 'Confirmed Journey'}
              </span>
            </motion.div>

            <h1 className="text-5xl md:text-7xl font-serif font-bold text-white mb-6 leading-tight">
              {trip.title}
            </h1>
            
            <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed max-w-3xl mx-auto">
              {trip.description}
            </p>

            {/* Trip Metadata */}
            <div className="flex flex-wrap justify-center gap-8 mb-12">
              <div className="flex items-center text-white/80">
                <Calendar className="w-5 h-5 mr-3 text-champagne-400" />
                <div className="text-left">
                  <div className="text-sm opacity-75">Duration</div>
                  <div className="font-semibold">{Math.ceil((trip.endDate.getTime() - trip.startDate.getTime()) / (1000 * 60 * 60 * 24))} Days</div>
                </div>
              </div>
              
              <div className="flex items-center text-white/80">
                <MapPin className="w-5 h-5 mr-3 text-champagne-400" />
                <div className="text-left">
                  <div className="text-sm opacity-75">Destinations</div>
                  <div className="font-semibold">{trip.destinations.length} Locations</div>
                </div>
              </div>
              
              <div className="flex items-center text-white/80">
                <Users className="w-5 h-5 mr-3 text-champagne-400" />
                <div className="text-left">
                  <div className="text-sm opacity-75">Guests</div>
                  <div className="font-semibold">{trip.guestCount} People</div>
                </div>
              </div>
            </div>

            {/* Investment Display */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="mb-8"
            >
              <div className="text-white/70 text-sm uppercase tracking-wider font-medium mb-2">Total Investment</div>
              <div className="text-4xl md:text-5xl font-serif font-bold text-champagne-400">
                {formatCurrency(trip.totalPrice)}
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.1 }}
              className="flex flex-wrap justify-center gap-4"
            >
              <Button 
                size="lg" 
                className="bg-champagne-gradient text-platinum-900 hover:scale-105 transition-transform"
              >
                Begin This Journey
              </Button>
              <Button 
                variant="ghost" 
                size="lg"
                className="bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                onClick={() => onAskQuestion('hero', `${trip.title} - General Inquiry`)}
              >
                <MessageSquare className="w-5 h-5 mr-2" />
                Ask Our Concierge
              </Button>
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60"
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
        >
          <div className="flex flex-col items-center">
            <div className="text-sm mb-2">Explore Journey</div>
            <div className="w-px h-8 bg-white/40"></div>
          </div>
        </motion.div>
      </motion.div>

      {/* Journey Timeline Section */}
      <div className="relative bg-white/95 dark:bg-platinum-900/95 backdrop-blur-sm">
        {/* Timeline Navigation */}
        <div className="sticky top-0 z-30 bg-white/95 dark:bg-platinum-900/95 backdrop-blur-sm border-b border-champagne-500/20">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-serif font-bold text-platinum-900 dark:text-platinum-50">
                Journey Timeline
              </h2>
              
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="text-platinum-600 dark:text-platinum-400"
                >
                  <Play className={`w-4 h-4 mr-2 ${isPlaying ? 'animate-pulse' : ''}`} />
                  {isPlaying ? 'Pause' : 'Auto Play'}
                </Button>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentDay(Math.max(0, currentDay - 1))}
                    disabled={currentDay === 0}
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <span className="text-sm text-platinum-600 dark:text-platinum-400 min-w-[80px] text-center">
                    Day {currentDay + 1} of {trip.itinerary.length}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentDay(Math.min(trip.itinerary.length - 1, currentDay + 1))}
                    disabled={currentDay === trip.itinerary.length - 1}
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Day Selector */}
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {trip.itinerary.map((day, index) => (
                <motion.button
                  key={day.id}
                  onClick={() => setCurrentDay(index)}
                  className={`flex-shrink-0 px-4 py-2 rounded-xl text-sm font-medium transition-all ${
                    index === currentDay
                      ? 'bg-champagne-gradient text-platinum-900 shadow-lg'
                      : 'bg-platinum-100 dark:bg-platinum-800 text-platinum-600 dark:text-platinum-400 hover:bg-platinum-200 dark:hover:bg-platinum-700'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Day {index + 1}
                </motion.button>
              ))}
            </div>
          </div>
        </div>

        {/* Current Day Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentDay}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5 }}
            className="max-w-7xl mx-auto px-6 py-12"
          >
            {currentItinerary && (
              <>
                {/* Day Header */}
                <div className="mb-12">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-champagne-gradient rounded-full flex items-center justify-center text-platinum-900 font-bold text-lg mr-4">
                      {currentDay + 1}
                    </div>
                    <div>
                      <h3 className="text-3xl font-serif font-bold text-platinum-900 dark:text-platinum-50">
                        {currentItinerary.title}
                      </h3>
                      <p className="text-platinum-600 dark:text-platinum-400">
                        {formatDate(currentItinerary.date)}
                      </p>
                    </div>
                  </div>
                  <p className="text-lg text-platinum-700 dark:text-platinum-300 max-w-4xl">
                    {currentItinerary.description}
                  </p>
                </div>

                {/* Day Image Gallery */}
                {currentItinerary.images.length > 0 && (
                  <div className="mb-12">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {currentItinerary.images.map((image, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="relative aspect-[4/3] rounded-2xl overflow-hidden group cursor-pointer"
                        >
                          <img
                            src={image}
                            alt={`${currentItinerary.title} - Image ${index + 1}`}
                            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="w-8 h-8 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center">
                              <Camera className="w-4 h-4 text-white" />
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Accommodation Section */}
                {currentItinerary.accommodation && (
                  <div className="mb-12">
                    <h4 className="text-2xl font-serif font-bold text-platinum-900 dark:text-platinum-50 mb-6">
                      Your Sanctuary
                    </h4>
                    <InteractiveCard hover className="overflow-hidden">
                      <div className="md:flex">
                        <div className="md:w-1/2">
                          <img
                            src={currentItinerary.accommodation.images[0]}
                            alt={currentItinerary.accommodation.name}
                            className="w-full h-64 md:h-full object-cover"
                          />
                        </div>
                        <div className="md:w-1/2 p-8">
                          <div className="flex justify-between items-start mb-4">
                            <div>
                              <h5 className="text-xl font-serif font-bold text-platinum-900 dark:text-platinum-50 mb-2">
                                {currentItinerary.accommodation.name}
                              </h5>
                              <p className="text-champagne-500 dark:text-champagne-400 font-medium">
                                {currentItinerary.accommodation.partnerName}
                              </p>
                              <p className="text-platinum-600 dark:text-platinum-400 text-sm">
                                {currentItinerary.accommodation.type}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-lg font-semibold text-platinum-900 dark:text-platinum-50">
                                {formatCurrency(currentItinerary.accommodation.cost)}
                              </p>
                              <p className="text-platinum-600 dark:text-platinum-400 text-sm">per night</p>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-2 mb-4">
                            {currentItinerary.accommodation.amenities.map((amenity, index) => (
                              <span
                                key={index}
                                className="px-3 py-1 bg-champagne-500/10 text-champagne-600 dark:text-champagne-400 rounded-full text-sm font-medium"
                              >
                                {amenity}
                              </span>
                            ))}
                          </div>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onAskQuestion(currentItinerary.id, `${currentItinerary.accommodation?.name} accommodation details`)}
                            className="text-platinum-600 dark:text-platinum-400 hover:text-champagne-500"
                          >
                            <MessageSquare className="w-4 h-4 mr-2" />
                            Ask about this accommodation
                          </Button>
                        </div>
                      </div>
                    </InteractiveCard>
                  </div>
                )}

                {/* Activities Section */}
                <div>
                  <h4 className="text-2xl font-serif font-bold text-platinum-900 dark:text-platinum-50 mb-6">
                    Today's Experiences
                  </h4>
                  <div className="space-y-8">
                    {currentItinerary.activities.map((activity, index) => (
                      <motion.div
                        key={activity.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <InteractiveCard hover className="overflow-hidden">
                          <div className={`md:flex ${index % 2 === 1 ? 'md:flex-row-reverse' : ''}`}>
                            <div className="md:w-2/5">
                              <img
                                src={activity.images[0]}
                                alt={activity.title}
                                className="w-full h-64 md:h-full object-cover"
                              />
                            </div>
                            <div className="md:w-3/5 p-8">
                              <div className="flex justify-between items-start mb-4">
                                <div className="flex-1">
                                  <div className="flex items-center mb-2">
                                    <Clock className="w-4 h-4 mr-2 text-champagne-500 dark:text-champagne-400" />
                                    <span className="text-sm text-platinum-600 dark:text-platinum-400">
                                      {activity.startTime} - {activity.endTime}
                                    </span>
                                  </div>
                                  <h5 className="text-xl font-serif font-bold text-platinum-900 dark:text-platinum-50 mb-2">
                                    {activity.title}
                                  </h5>
                                  <p className="text-champagne-500 dark:text-champagne-400 font-medium mb-1">
                                    {activity.partnerName}
                                  </p>
                                  <div className="flex items-center text-platinum-600 dark:text-platinum-400 text-sm mb-4">
                                    <MapPin className="w-4 h-4 mr-1" />
                                    {activity.location}
                                  </div>
                                </div>
                                <div className="text-right ml-4">
                                  <p className="text-lg font-semibold text-platinum-900 dark:text-platinum-50">
                                    {formatCurrency(activity.cost)}
                                  </p>
                                  <div className="flex items-center mt-1">
                                    <Star className="w-4 h-4 text-champagne-400 fill-current mr-1" />
                                    <span className="text-sm text-platinum-600 dark:text-platinum-400">Exclusive</span>
                                  </div>
                                </div>
                              </div>

                              <p className="text-platinum-700 dark:text-platinum-300 mb-6 leading-relaxed">
                                {activity.description}
                              </p>

                              <div className="flex items-center justify-between">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => onAskQuestion(currentItinerary.id, `${activity.title} at ${activity.location}`)}
                                  className="text-platinum-600 dark:text-platinum-400 hover:text-champagne-500"
                                >
                                  <MessageSquare className="w-4 h-4 mr-2" />
                                  Ask about this experience
                                </Button>

                                <div className="text-sm text-platinum-500 dark:text-platinum-500">
                                  Experience #{index + 1}
                                </div>
                              </div>
                            </div>
                          </div>
                        </InteractiveCard>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}
