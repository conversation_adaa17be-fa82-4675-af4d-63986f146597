import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import { 
  ArrowLeft, 
  Calendar, 
  MapPin, 
  Users, 
  Star, 
  MessageSquare, 
  Share2, 
  Heart,
  Play,
  ChevronLeft,
  ChevronRight,
  Clock,
  Camera,
  Sparkles
} from 'lucide-react';
import { Trip, ItineraryDay } from '../../types';
import { formatCurrency, formatDate } from '../../lib/utils';
import { Button } from '../ui/Button';
import { InteractiveCard } from '../ui/InteractiveCard';
import { AccommodationModal } from './AccommodationModal';

interface VVIPTripExperienceProps {
  trip: Trip;
  onBack: () => void;
  onAskQuestion: (dayId: string, context: string) => void;
}

export function VVIPTripExperience({ trip, onBack, onAskQuestion }: VVIPTripExperienceProps) {
  const [currentDay, setCurrentDay] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentActivityIndex, setCurrentActivityIndex] = useState(0);
  const [showQuoteActions, setShowQuoteActions] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<string | null>(null);
  const [isEditingQuote, setIsEditingQuote] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [mouseMoving, setMouseMoving] = useState(false);
  const [heroCollapsed, setHeroCollapsed] = useState(false);
  const [dayHeaderCollapsed, setDayHeaderCollapsed] = useState(false);
  const [selectedAccommodation, setSelectedAccommodation] = useState<string>('luxury');
  const [selectedActivityChoices, setSelectedActivityChoices] = useState<{[key: string]: string}>({});
  const [totalPrice, setTotalPrice] = useState(trip.totalPrice);
  const [accommodationModalOpen, setAccommodationModalOpen] = useState(false);
  const [selectedAccommodationForModal, setSelectedAccommodationForModal] = useState<string | null>(null);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const heroY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const heroOpacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  // Auto-collapse hero when scrolling
  useEffect(() => {
    const unsubscribe = scrollYProgress.onChange((latest) => {
      setHeroCollapsed(latest > 0.3);
    });
    return unsubscribe;
  }, [scrollYProgress]);

  const currentItinerary = trip.itinerary[currentDay];
  const allImages = trip.itinerary.flatMap(day => [
    ...day.images,
    ...(day.accommodation?.images || []),
    ...day.activities.flatMap(activity => activity.images)
  ]);

  // Mouse movement detection for pause
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    const handleMouseMove = () => {
      setMouseMoving(true);
      clearTimeout(timeout);
      timeout = setTimeout(() => setMouseMoving(false), 2000);
    };

    if (isPlaying) {
      document.addEventListener('mousemove', handleMouseMove);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        clearTimeout(timeout);
      };
    }
  }, [isPlaying]);

  // Cinematic auto-play through all images and activities
  useEffect(() => {
    if (isPlaying && !mouseMoving) {
      const interval = setInterval(() => {
        const currentDayData = trip.itinerary[currentDay];
        const totalActivities = currentDayData.activities.length;

        if (currentActivityIndex < totalActivities - 1) {
          setCurrentActivityIndex(prev => prev + 1);
        } else {
          // Move to next day
          if (currentDay < trip.itinerary.length - 1) {
            setCurrentDay(prev => prev + 1);
            setCurrentActivityIndex(0);
          } else {
            // Reset to beginning
            setCurrentDay(0);
            setCurrentActivityIndex(0);
          }
        }
      }, 3000); // 3 seconds per activity/image
      return () => clearInterval(interval);
    }
  }, [isPlaying, mouseMoving, currentDay, currentActivityIndex, trip.itinerary.length]);

  const handleBeginJourney = () => {
    // Scroll to the journey content
    const journeySection = document.querySelector('[data-journey-content]');
    if (journeySection) {
      journeySection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleAcceptQuote = () => {
    // Implementation for accepting quote
    console.log('Quote accepted for trip:', trip.id);
  };

  const handleAmendQuote = () => {
    setIsEditingQuote(true);
  };

  // Accommodation options for each day
  const accommodationOptions = {
    luxury: {
      id: 'luxury',
      name: 'Royal Mansour Presidential Riad',
      type: 'Private Riad with Pool',
      cost: 18000,
      images: [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1544735716-392fe2489ffa?auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1540962351504-03099e0a754b?auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?auto=format&fit=crop&w=800&q=80'
      ],
      amenities: ['Private Pool', 'Personal Butler', 'Rooftop Terrace', 'Spa Access', 'Private Chef'],
      partnerName: 'Royal Mansour',
      description: 'Ultimate luxury in a private riad with dedicated staff and exclusive amenities.',
      rating: 5.0,
      location: 'Marrakech Medina',
      rooms: 4,
      maxGuests: 8,
      highlights: [
        'Exclusive private riad in the heart of Marrakech',
        'Dedicated team of 8 staff members',
        'Private rooftop pool with Atlas Mountain views',
        'Michelin-starred private chef available',
        'Traditional hammam and spa treatments',
        'Helicopter landing pad access'
      ],
      detailedDescription: 'Experience the pinnacle of Moroccan luxury in this extraordinary private riad within the Royal Mansour complex. This palatial residence features traditional Moroccan architecture with contemporary amenities, offering an unparalleled level of privacy and service. The riad includes four opulent suites, each with its own private terrace, a central courtyard with a heated pool, and access to all Royal Mansour facilities including the award-winning spa and multiple restaurants.'
    },
    premium: {
      id: 'premium',
      name: 'La Mamounia Royal Suite',
      type: 'Royal Suite',
      cost: 12000,
      images: [
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=800&q=80'
      ],
      amenities: ['Garden View', 'Butler Service', 'Spa Access', 'Private Terrace'],
      partnerName: 'La Mamounia',
      description: 'Iconic luxury hotel suite with legendary service and stunning gardens.'
    },
    boutique: {
      id: 'boutique',
      name: 'Riad Farnatchi Exclusive',
      type: 'Boutique Riad',
      cost: 8000,
      images: [
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1544735716-392fe2489ffa?auto=format&fit=crop&w=800&q=80'
      ],
      amenities: ['Authentic Design', 'Rooftop Pool', 'Concierge', 'Traditional Hammam'],
      partnerName: 'Riad Farnatchi',
      description: 'Intimate boutique experience with authentic Moroccan charm and modern luxury.'
    }
  };

  // Activity choice options
  const getActivityChoices = (activityId: string) => {
    const baseChoices = {
      standard: { label: 'Standard Experience', priceMultiplier: 1 },
      premium: { label: 'Premium Experience', priceMultiplier: 1.3 },
      exclusive: { label: 'Ultra-Exclusive', priceMultiplier: 1.8 }
    };
    return baseChoices;
  };

  // Update total price when selections change
  useEffect(() => {
    const accommodationCost = accommodationOptions[selectedAccommodation as keyof typeof accommodationOptions].cost;

    let activityCosts = 0;
    if (currentItinerary) {
      activityCosts = currentItinerary.activities.reduce((sum, activity) => {
        const choice = selectedActivityChoices[activity.id] || 'standard';
        const multiplier = getActivityChoices(activity.id)[choice as keyof typeof getActivityChoices]?.priceMultiplier || 1;
        return sum + (activity.cost * multiplier);
      }, 0);
    }

    // Base calculation - accommodation + activities
    setTotalPrice(accommodationCost + activityCosts);
  }, [selectedAccommodation, selectedActivityChoices, currentItinerary]);

  return (
    <div ref={containerRef} className="min-h-screen bg-luxury-gradient-light dark:bg-luxury-gradient">
      {/* Immersive Hero Section */}
      <motion.div 
        className="relative h-screen overflow-hidden"
        style={{ y: heroY, opacity: heroOpacity }}
      >
        {/* Cinematic Background with Auto-changing Images */}
        <div className="absolute inset-0">
          <AnimatePresence mode="wait">
            <motion.img
              key={isPlaying ? `${currentDay}-${currentActivityIndex}` : 'hero'}
              src={isPlaying && currentItinerary?.activities[currentActivityIndex]?.images[0]
                ? currentItinerary.activities[currentActivityIndex].images[0]
                : trip.heroImage}
              alt={trip.title}
              className="w-full h-full object-cover"
              initial={{ scale: 1.1, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              transition={{ duration: 1.5, ease: "easeOut" }}
            />
          </AnimatePresence>
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-transparent to-transparent" />

          {/* Cinematic Progress Indicator */}
          {isPlaying && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
              <div className="flex items-center space-x-2 bg-black/40 backdrop-blur-md rounded-full px-4 py-2">
                <div className="text-white/80 text-sm">
                  Day {currentDay + 1} • {currentItinerary?.activities[currentActivityIndex]?.title || 'Experience'}
                </div>
                <div className="w-24 h-1 bg-white/20 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-champagne-400"
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    transition={{ duration: 3, ease: "linear" }}
                    key={`${currentDay}-${currentActivityIndex}`}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Floating Navigation */}
        <motion.div 
          className="absolute top-8 left-8 z-20"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Button
            variant="ghost"
            onClick={onBack}
            className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Proposals
          </Button>
        </motion.div>

        {/* Trip Actions */}
        <motion.div
          className="absolute top-8 right-8 z-20 flex space-x-3"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsEditingQuote(true)}
            className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
          >
            <span className="text-xs mr-2">Edit Quote</span>
            ✏️
          </Button>
          <Button variant="ghost" size="sm" className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20">
            <Heart className="w-5 h-5" />
          </Button>
          <Button variant="ghost" size="sm" className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20">
            <Share2 className="w-5 h-5" />
          </Button>
          <Button variant="ghost" size="sm" className="bg-black/20 backdrop-blur-md border border-white/20 text-white hover:bg-white/20">
            <MessageSquare className="w-5 h-5" />
          </Button>
        </motion.div>

        {/* Hero Content */}
        <div className="absolute inset-0 flex items-center justify-center text-center z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="max-w-4xl px-8"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
              className="mb-6"
            >
              <span className="inline-flex items-center px-4 py-2 bg-champagne-500/20 backdrop-blur-md border border-champagne-400/30 rounded-full text-champagne-300 text-sm font-medium uppercase tracking-wider">
                <Sparkles className="w-4 h-4 mr-2" />
                {trip.status === 'proposed' ? 'Exclusive Proposal' : 'Confirmed Journey'}
              </span>
            </motion.div>

            <h1 className="text-5xl md:text-7xl font-serif font-bold text-white mb-6 leading-tight">
              {trip.title}
            </h1>
            
            <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed max-w-3xl mx-auto">
              {trip.description}
            </p>

            {/* Trip Metadata */}
            <div className="flex flex-wrap justify-center gap-8 mb-12">
              <div className="flex items-center text-white/80">
                <Calendar className="w-5 h-5 mr-3 text-champagne-400" />
                <div className="text-left">
                  <div className="text-sm opacity-75">Duration</div>
                  <div className="font-semibold">{Math.ceil((trip.endDate.getTime() - trip.startDate.getTime()) / (1000 * 60 * 60 * 24))} Days</div>
                </div>
              </div>
              
              <div className="flex items-center text-white/80">
                <MapPin className="w-5 h-5 mr-3 text-champagne-400" />
                <div className="text-left">
                  <div className="text-sm opacity-75">Destinations</div>
                  <div className="font-semibold">{trip.destinations.length} Locations</div>
                </div>
              </div>
              
              <div className="flex items-center text-white/80">
                <Users className="w-5 h-5 mr-3 text-champagne-400" />
                <div className="text-left">
                  <div className="text-sm opacity-75">Guests</div>
                  <div className="font-semibold">{trip.guestCount} People</div>
                </div>
              </div>
            </div>

            {/* Investment Display */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="mb-8"
            >
              <div className="text-white/70 text-sm uppercase tracking-wider font-medium mb-2">Total Investment</div>
              <div className="text-4xl md:text-5xl font-serif font-bold text-champagne-400">
                {formatCurrency(totalPrice)}
              </div>
              {totalPrice !== trip.totalPrice && (
                <div className="text-white/60 text-lg line-through">
                  {formatCurrency(trip.totalPrice)}
                </div>
              )}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.1 }}
              className="flex flex-wrap justify-center gap-4"
            >
              <Button
                size="lg"
                onClick={handleBeginJourney}
                className="bg-champagne-gradient text-platinum-900 hover:scale-105 transition-transform"
              >
                Begin This Journey
              </Button>
              <Button
                variant="ghost"
                size="lg"
                className="bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                onClick={() => onAskQuestion('hero', `${trip.title} - General Inquiry`)}
              >
                <MessageSquare className="w-5 h-5 mr-2" />
                Ask Our Concierge
              </Button>
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60"
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
        >
          <div className="flex flex-col items-center">
            <div className="text-sm mb-2">Explore Journey</div>
            <div className="w-px h-8 bg-white/40"></div>
          </div>
        </motion.div>
      </motion.div>

      {/* Sticky Collapsed Hero */}
      <AnimatePresence>
        {heroCollapsed && (
          <motion.div
            initial={{ y: -100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -100, opacity: 0 }}
            className="fixed top-0 left-0 right-0 z-40 bg-white/95 dark:bg-platinum-900/95 backdrop-blur-md border-b border-champagne-500/20"
          >
            <div className="max-w-7xl mx-auto px-6 py-4">
              <div className="flex items-center justify-between">
                {/* Trip Info */}
                <div className="flex items-center space-x-6">
                  <Button
                    variant="ghost"
                    onClick={onBack}
                    className="text-slate-600 dark:text-platinum-400 hover:text-champagne-500"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back
                  </Button>

                  <div className="flex items-center space-x-4">
                    {/* Cinematic Preview */}
                    <div className="relative w-16 h-12 rounded-lg overflow-hidden">
                      <AnimatePresence mode="wait">
                        <motion.img
                          key={isPlaying ? `${currentDay}-${currentActivityIndex}` : 'hero'}
                          src={isPlaying && currentItinerary?.activities[currentActivityIndex]?.images[0]
                            ? currentItinerary.activities[currentActivityIndex].images[0]
                            : trip.heroImage}
                          alt={trip.title}
                          className="w-full h-full object-cover"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.5 }}
                        />
                      </AnimatePresence>
                      {isPlaying && (
                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/20">
                          <motion.div
                            className="h-full bg-champagne-400"
                            initial={{ width: 0 }}
                            animate={{ width: '100%' }}
                            transition={{ duration: 3, ease: "linear" }}
                            key={`${currentDay}-${currentActivityIndex}`}
                          />
                        </div>
                      )}
                    </div>

                    <div>
                      <h3 className="font-serif font-bold text-slate-900 dark:text-platinum-50 text-lg">
                        {trip.title}
                      </h3>
                      <p className="text-sm text-slate-600 dark:text-platinum-400">
                        Day {currentDay + 1} • {currentItinerary?.title}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Controls */}
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-sm text-slate-600 dark:text-platinum-400">Total Investment</div>
                    <div className="text-lg font-serif font-bold text-champagne-600 dark:text-champagne-400">
                      {formatCurrency(totalPrice)}
                    </div>
                    {totalPrice !== trip.totalPrice && (
                      <div className="text-xs text-slate-500 dark:text-platinum-500 line-through">
                        {formatCurrency(trip.totalPrice)}
                      </div>
                    )}
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="text-slate-600 dark:text-platinum-400"
                  >
                    <Play className={`w-4 h-4 mr-2 ${isPlaying ? 'animate-pulse' : ''}`} />
                    {isPlaying ? 'Pause' : 'Play'}
                  </Button>

                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={handleAcceptQuote}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      Accept
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleAmendQuote}
                      className="bg-champagne-gradient text-platinum-900"
                    >
                      Amend
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onAskQuestion('hero', `${trip.title} - Proposal Discussion`)}
                      className="text-slate-600 dark:text-platinum-400 hover:text-champagne-500"
                    >
                      Discuss
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Journey Timeline Section */}
      <div className={`relative bg-white/95 dark:bg-platinum-900/95 backdrop-blur-sm ${heroCollapsed ? 'pt-20' : ''}`} data-journey-content>
        {/* Timeline Navigation */}
        <div className="sticky top-0 z-30 bg-white/95 dark:bg-platinum-900/95 backdrop-blur-sm border-b border-champagne-500/20">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-serif font-bold text-platinum-900 dark:text-platinum-50">
                Journey Timeline
              </h2>
              
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="text-platinum-600 dark:text-platinum-400"
                >
                  <Play className={`w-4 h-4 mr-2 ${isPlaying ? 'animate-pulse' : ''}`} />
                  {isPlaying ? 'Pause' : 'Auto Play'}
                </Button>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentDay(Math.max(0, currentDay - 1))}
                    disabled={currentDay === 0}
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <span className="text-sm text-platinum-600 dark:text-platinum-400 min-w-[80px] text-center">
                    Day {currentDay + 1} of {trip.itinerary.length}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentDay(Math.min(trip.itinerary.length - 1, currentDay + 1))}
                    disabled={currentDay === trip.itinerary.length - 1}
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Day Selector */}
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {trip.itinerary.map((day, index) => (
                <motion.button
                  key={day.id}
                  onClick={() => setCurrentDay(index)}
                  className={`flex-shrink-0 px-4 py-2 rounded-xl text-sm font-medium transition-all ${
                    index === currentDay
                      ? 'bg-champagne-gradient text-platinum-900 shadow-lg'
                      : 'bg-platinum-100 dark:bg-platinum-800 text-platinum-600 dark:text-platinum-400 hover:bg-platinum-200 dark:hover:bg-platinum-700'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Day {index + 1}
                </motion.button>
              ))}
            </div>
          </div>
        </div>

        {/* Current Day Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentDay}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5 }}
            className="max-w-7xl mx-auto px-6 py-12"
          >
            {currentItinerary && (
              <>
                {/* Sticky Day Header with Background Image */}
                <div className="mb-12">
                  <div className="sticky top-0 z-30 mb-8">
                    <motion.div
                      className="relative rounded-3xl overflow-hidden"
                      animate={{
                        height: dayHeaderCollapsed ? 120 : 300
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      {/* Background Image */}
                      <div className="absolute inset-0">
                        <img
                          src={currentItinerary.images[0]}
                          alt={currentItinerary.title}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-black/70" />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/30" />
                      </div>

                      {/* Content */}
                      <div className="relative p-8 h-full flex flex-col justify-between">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center">
                            <div className="relative">
                              <div className="w-16 h-16 bg-champagne-gradient rounded-2xl flex items-center justify-center text-platinum-900 font-bold text-xl mr-6 shadow-lg">
                                {currentDay + 1}
                              </div>
                              <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs">✓</span>
                              </div>
                            </div>
                            <div>
                              <h3 className="text-4xl font-serif font-bold text-white mb-2">
                                {currentItinerary.title}
                              </h3>
                              <div className="flex items-center space-x-4 text-white/80">
                                <div className="flex items-center">
                                  <Calendar className="w-4 h-4 mr-2" />
                                  {formatDate(currentItinerary.date)}
                                </div>
                                <div className="flex items-center">
                                  <Clock className="w-4 h-4 mr-2" />
                                  {currentItinerary.activities.length} Experiences
                                </div>
                                <div className="flex items-center">
                                  <MapPin className="w-4 h-4 mr-2" />
                                  {currentItinerary.activities[0]?.location.split(',')[0]}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Day Actions */}
                          <div className="flex items-center space-x-3">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onAskQuestion(currentItinerary.id, `Day ${currentDay + 1} - ${currentItinerary.title}`)}
                              className="bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                            >
                              <MessageSquare className="w-4 h-4 mr-2" />
                              Ask About This Day
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setDayHeaderCollapsed(!dayHeaderCollapsed)}
                              className="bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20"
                            >
                              {dayHeaderCollapsed ? '↓' : '↑'}
                            </Button>
                          </div>
                        </div>

                        {!dayHeaderCollapsed && (
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className="mt-6"
                          >
                            <p className="text-lg text-white/90 leading-relaxed max-w-4xl mb-6">
                              {currentItinerary.description}
                            </p>

                            {/* Day Stats */}
                            <div className="flex items-center space-x-8">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-champagne-400">
                                  {currentItinerary.activities.length}
                                </div>
                                <div className="text-sm text-white/70">Experiences</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-champagne-400">
                                  {formatCurrency(currentItinerary.activities.reduce((sum, activity) => {
                                    const choice = selectedActivityChoices[activity.id] || 'standard';
                                    const multiplier = getActivityChoices(activity.id)[choice as keyof typeof getActivityChoices]?.priceMultiplier || 1;
                                    return sum + (activity.cost * multiplier);
                                  }, 0))}
                                </div>
                                <div className="text-sm text-white/70">Day Investment</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-champagne-400">
                                  {Math.ceil((new Date(currentItinerary.activities[currentItinerary.activities.length - 1]?.endTime || '23:00').getTime() -
                                            new Date(currentItinerary.activities[0]?.startTime || '09:00').getTime()) / (1000 * 60 * 60))}h
                                </div>
                                <div className="text-sm text-white/70">Duration</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-champagne-400">
                                  {formatCurrency(totalPrice)}
                                </div>
                                <div className="text-sm text-white/70">Total Trip</div>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </div>
                    </motion.div>
                  </div>

                {/* Day Image Gallery */}
                {currentItinerary.images.length > 0 && (
                  <div className="mb-12">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {currentItinerary.images.map((image, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="relative aspect-[4/3] rounded-2xl overflow-hidden group cursor-pointer"
                        >
                          <img
                            src={image}
                            alt={`${currentItinerary.title} - Image ${index + 1}`}
                            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="w-8 h-8 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center">
                              <Camera className="w-4 h-4 text-white" />
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Interactive Accommodation Selector */}
                <div className="mb-12">
                  <div className="flex items-center justify-between mb-6">
                    <h4 className="text-2xl font-serif font-bold text-slate-900 dark:text-platinum-50">
                      Choose Your Sanctuary
                    </h4>
                    <div className="text-right">
                      <div className="text-sm text-slate-600 dark:text-platinum-400">From</div>
                      <div className="text-lg font-semibold text-champagne-600 dark:text-champagne-400">
                        {formatCurrency(accommodationOptions[selectedAccommodation as keyof typeof accommodationOptions].cost)}/night
                      </div>
                    </div>
                  </div>

                  {/* Accommodation Options */}
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {Object.values(accommodationOptions).map((option) => (
                      <motion.div
                        key={option.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="relative"
                      >
                        <InteractiveCard
                          hover
                          className={`overflow-hidden cursor-pointer transition-all duration-300 ${
                            selectedAccommodation === option.id
                              ? 'ring-2 ring-champagne-500 shadow-xl scale-105'
                              : 'hover:shadow-lg'
                          }`}
                          onClick={() => setSelectedAccommodation(option.id)}
                        >
                          {/* Selection Indicator */}
                          {selectedAccommodation === option.id && (
                            <div className="absolute top-4 right-4 z-10">
                              <div className="w-8 h-8 bg-champagne-500 rounded-full flex items-center justify-center shadow-lg">
                                <span className="text-white text-sm">✓</span>
                              </div>
                            </div>
                          )}

                          {/* Image Gallery */}
                          <div className="relative h-48">
                            <img
                              src={option.images[0]}
                              alt={option.name}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                            {/* Price Badge */}
                            <div className="absolute bottom-4 left-4">
                              <div className="bg-black/60 backdrop-blur-md rounded-full px-3 py-1 text-white">
                                <span className="text-lg font-semibold">{formatCurrency(option.cost)}</span>
                                <span className="text-sm opacity-80">/night</span>
                              </div>
                            </div>
                          </div>

                          {/* Content */}
                          <div className="p-6">
                            <div className="mb-4">
                              <h5 className="text-lg font-serif font-bold text-slate-900 dark:text-platinum-50 mb-1">
                                {option.name}
                              </h5>
                              <p className="text-champagne-600 dark:text-champagne-400 font-medium text-sm">
                                {option.partnerName}
                              </p>
                              <p className="text-slate-600 dark:text-platinum-400 text-sm">
                                {option.type}
                              </p>
                            </div>

                            <p className="text-slate-700 dark:text-platinum-300 text-sm mb-4 leading-relaxed">
                              {option.description}
                            </p>

                            {/* Amenities */}
                            <div className="flex flex-wrap gap-1 mb-4">
                              {option.amenities.slice(0, 3).map((amenity, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-champagne-500/10 text-champagne-600 dark:text-champagne-400 rounded text-xs"
                                >
                                  {amenity}
                                </span>
                              ))}
                              {option.amenities.length > 3 && (
                                <span className="px-2 py-1 bg-slate-100 dark:bg-platinum-800 text-slate-600 dark:text-platinum-400 rounded text-xs">
                                  +{option.amenities.length - 3} more
                                </span>
                              )}
                            </div>

                            {/* Actions */}
                            <div className="flex items-center justify-between">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedAccommodationForModal(option.id);
                                  setAccommodationModalOpen(true);
                                }}
                                className="text-slate-600 dark:text-platinum-400 hover:text-champagne-500 text-xs"
                              >
                                View Details
                              </Button>

                              {selectedAccommodation === option.id && (
                                <span className="text-xs text-champagne-600 dark:text-champagne-400 font-medium">
                                  Selected
                                </span>
                              )}
                            </div>
                          </div>
                        </InteractiveCard>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Revolutionary Masonry Activities Layout */}
                <div>
                  <h4 className="text-2xl font-serif font-bold text-slate-900 dark:text-platinum-50 mb-6">
                    Today's Experiences
                  </h4>

                  {/* Masonry Grid */}
                  <div className="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6">
                    {currentItinerary.activities.map((activity, index) => (
                      <motion.div
                        key={activity.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="break-inside-avoid mb-6"
                      >
                        <InteractiveCard
                          hover
                          className="overflow-hidden cursor-pointer group"
                          onClick={() => setSelectedActivity(selectedActivity === activity.id ? null : activity.id)}
                        >
                          {/* Activity Image */}
                          <div className="relative">
                            <img
                              src={activity.images[0]}
                              alt={activity.title}
                              className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                            {/* Time Badge */}
                            <div className="absolute top-4 left-4">
                              <div className="bg-black/40 backdrop-blur-md rounded-full px-3 py-1 text-white text-sm">
                                {activity.startTime} - {activity.endTime}
                              </div>
                            </div>

                            {/* Price Badge */}
                            <div className="absolute top-4 right-4">
                              <div className="bg-champagne-500/90 backdrop-blur-md rounded-full px-3 py-1 text-platinum-900 text-sm font-semibold">
                                {formatCurrency(activity.cost * (getActivityChoices(activity.id)[selectedActivityChoices[activity.id] as keyof typeof getActivityChoices]?.priceMultiplier || 1))}
                              </div>
                            </div>

                            {/* Expand Indicator */}
                            <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <div className="w-8 h-8 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center">
                                <Camera className="w-4 h-4 text-white" />
                              </div>
                            </div>
                          </div>

                          {/* Activity Content */}
                          <div className="p-6">
                            <div className="mb-3">
                              <h5 className="text-lg font-serif font-bold text-slate-900 dark:text-platinum-50 mb-1">
                                {activity.title}
                              </h5>
                              <p className="text-champagne-600 dark:text-champagne-400 font-medium text-sm">
                                {activity.partnerName}
                              </p>
                              <div className="flex items-center text-slate-600 dark:text-platinum-400 text-sm mt-1">
                                <MapPin className="w-3 h-3 mr-1" />
                                {activity.location}
                              </div>
                            </div>

                            <p className="text-slate-700 dark:text-platinum-300 text-sm leading-relaxed mb-4">
                              {activity.description}
                            </p>

                            {/* Interactive Actions */}
                            <div className="flex items-center justify-between">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onAskQuestion(currentItinerary.id, `${activity.title} at ${activity.location}`);
                                }}
                                className="text-slate-600 dark:text-platinum-400 hover:text-champagne-500 text-xs"
                              >
                                <MessageSquare className="w-3 h-3 mr-1" />
                                Ask Concierge
                              </Button>

                              <div className="flex items-center space-x-2">
                                <Star className="w-4 h-4 text-champagne-400 fill-current" />
                                <span className="text-xs text-slate-500 dark:text-platinum-500">Exclusive</span>
                              </div>
                            </div>
                          </div>

                          {/* Expanded Content */}
                          <AnimatePresence>
                            {selectedActivity === activity.id && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: 'auto', opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.3 }}
                                className="border-t border-champagne-500/20 overflow-hidden"
                              >
                                <div className="p-6">
                                  {/* Additional Images */}
                                  {activity.images.length > 1 && (
                                    <div className="grid grid-cols-2 gap-2 mb-4">
                                      {activity.images.slice(1, 5).map((image, imgIndex) => (
                                        <img
                                          key={imgIndex}
                                          src={image}
                                          alt={`${activity.title} - Image ${imgIndex + 2}`}
                                          className="w-full h-20 object-cover rounded-lg"
                                        />
                                      ))}
                                    </div>
                                  )}

                                  {/* Experience Level Selector */}
                                  <div className="space-y-4">
                                    <div>
                                      <h6 className="font-semibold text-slate-900 dark:text-platinum-50 text-sm mb-3">
                                        Choose Experience Level
                                      </h6>
                                      <div className="space-y-2">
                                        {Object.entries(getActivityChoices(activity.id)).map(([key, choice]) => (
                                          <label
                                            key={key}
                                            className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-all ${
                                              (selectedActivityChoices[activity.id] || 'standard') === key
                                                ? 'border-champagne-500 bg-champagne-50 dark:bg-champagne-900/20'
                                                : 'border-slate-200 dark:border-platinum-700 hover:border-champagne-300'
                                            }`}
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              setSelectedActivityChoices(prev => ({
                                                ...prev,
                                                [activity.id]: key
                                              }));
                                            }}
                                          >
                                            <div className="flex items-center">
                                              <input
                                                type="radio"
                                                name={`activity-${activity.id}`}
                                                value={key}
                                                checked={(selectedActivityChoices[activity.id] || 'standard') === key}
                                                onChange={() => {}}
                                                className="w-4 h-4 text-champagne-500 border-slate-300 focus:ring-champagne-500"
                                              />
                                              <div className="ml-3">
                                                <div className="text-sm font-medium text-slate-900 dark:text-platinum-50">
                                                  {choice.label}
                                                </div>
                                                <div className="text-xs text-slate-600 dark:text-platinum-400">
                                                  {formatCurrency(activity.cost * choice.priceMultiplier)}
                                                </div>
                                              </div>
                                            </div>
                                            {(selectedActivityChoices[activity.id] || 'standard') === key && (
                                              <div className="w-5 h-5 bg-champagne-500 rounded-full flex items-center justify-center">
                                                <span className="text-white text-xs">✓</span>
                                              </div>
                                            )}
                                          </label>
                                        ))}
                                      </div>
                                    </div>

                                    <div>
                                      <h6 className="font-semibold text-slate-900 dark:text-platinum-50 text-sm mb-1">
                                        What's Included
                                      </h6>
                                      <p className="text-slate-600 dark:text-platinum-400 text-xs">
                                        Private guide, luxury transportation, exclusive access, refreshments
                                      </p>
                                    </div>

                                    <div>
                                      <h6 className="font-semibold text-slate-900 dark:text-platinum-50 text-sm mb-1">
                                        Duration
                                      </h6>
                                      <p className="text-slate-600 dark:text-platinum-400 text-xs">
                                        Approximately 3-4 hours
                                      </p>
                                    </div>

                                    <div className="flex space-x-2 pt-2">
                                      <Button
                                        size="sm"
                                        className="bg-champagne-gradient text-platinum-900 text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          onAskQuestion(currentItinerary.id, `Customize ${activity.title}`);
                                        }}
                                      >
                                        Ask to Customize
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="text-slate-600 dark:text-platinum-400 text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          // Handle remove activity
                                        }}
                                      >
                                        Remove
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </InteractiveCard>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Quote Editing Modal */}
      <AnimatePresence>
        {isEditingQuote && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setIsEditingQuote(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-platinum-900 rounded-2xl p-8 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-serif font-bold text-slate-900 dark:text-platinum-50">
                  Customize Your Journey
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsEditingQuote(false)}
                  className="text-slate-600 dark:text-platinum-400"
                >
                  ✕
                </Button>
              </div>

              <div className="space-y-6">
                {/* Trip Preferences */}
                <div>
                  <h4 className="font-semibold text-slate-900 dark:text-platinum-50 mb-3">
                    Travel Preferences
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-platinum-300 mb-2">
                        Guest Count
                      </label>
                      <input
                        type="number"
                        defaultValue={trip.guestCount}
                        className="w-full px-3 py-2 border border-slate-300 dark:border-platinum-600 rounded-lg bg-white dark:bg-platinum-800 text-slate-900 dark:text-platinum-50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-platinum-300 mb-2">
                        Travel Dates
                      </label>
                      <input
                        type="date"
                        defaultValue={trip.startDate.toISOString().split('T')[0]}
                        className="w-full px-3 py-2 border border-slate-300 dark:border-platinum-600 rounded-lg bg-white dark:bg-platinum-800 text-slate-900 dark:text-platinum-50"
                      />
                    </div>
                  </div>
                </div>

                {/* Special Requests */}
                <div>
                  <h4 className="font-semibold text-slate-900 dark:text-platinum-50 mb-3">
                    Special Requests
                  </h4>
                  <textarea
                    rows={4}
                    placeholder="Tell us about any special requirements, dietary restrictions, accessibility needs, or preferences..."
                    className="w-full px-3 py-2 border border-slate-300 dark:border-platinum-600 rounded-lg bg-white dark:bg-platinum-800 text-slate-900 dark:text-platinum-50 placeholder-slate-500 dark:placeholder-platinum-400"
                  />
                </div>

                {/* Budget Preferences */}
                <div>
                  <h4 className="font-semibold text-slate-900 dark:text-platinum-50 mb-3">
                    Investment Range
                  </h4>
                  <div className="flex items-center space-x-4">
                    <input
                      type="range"
                      min="50000"
                      max="500000"
                      step="10000"
                      defaultValue={trip.totalPrice}
                      className="flex-1"
                    />
                    <span className="text-champagne-600 dark:text-champagne-400 font-semibold">
                      {formatCurrency(trip.totalPrice)}
                    </span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 pt-4">
                  <Button
                    className="flex-1 bg-champagne-gradient text-platinum-900"
                    onClick={() => {
                      // Handle save changes
                      setIsEditingQuote(false);
                    }}
                  >
                    Save Changes
                  </Button>
                  <Button
                    variant="ghost"
                    className="flex-1 text-slate-600 dark:text-platinum-400"
                    onClick={() => setIsEditingQuote(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Accommodation Modal */}
      {selectedAccommodationForModal && (
        <AccommodationModal
          isOpen={accommodationModalOpen}
          onClose={() => {
            setAccommodationModalOpen(false);
            setSelectedAccommodationForModal(null);
          }}
          accommodation={accommodationOptions[selectedAccommodationForModal as keyof typeof accommodationOptions]}
          onSelect={(id) => {
            setSelectedAccommodation(id);
            setAccommodationModalOpen(false);
            setSelectedAccommodationForModal(null);
          }}
          isSelected={selectedAccommodation === selectedAccommodationForModal}
          onAskQuestion={onAskQuestion}
        />
      )}
    </div>
  );
}
