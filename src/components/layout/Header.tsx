import React from 'react';
import { motion } from 'framer-motion';
import { Crown, Bell, User, LogOut } from 'lucide-react';
import { useAuthState } from '../../hooks/useAuth';
import { Button } from '../ui/Button';
import { LuxuryThemeToggle } from '../ui/ThemeToggle';

export function Header() {
  const { user, logout } = useAuthState();

  if (!user) return null;

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className="bg-white/95 dark:bg-platinum-950/95 border-b border-champagne-500/20 sticky top-0 z-50 backdrop-blur-sm transition-colors duration-300"
    >
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-champagne-gradient rounded-xl flex items-center justify-center shadow-lg">
              <Crown className="w-6 h-6 text-platinum-900" />
            </div>
            <div>
              <h1 className="text-2xl font-serif font-bold text-platinum-900 dark:text-platinum-50">Opulence</h1>
              <p className="text-xs text-champagne-400 font-medium tracking-wider uppercase">
                {user.role === 'vvip' ? 'VVIP Experience' : 
                 user.role === 'partner' ? 'Partner Portal' : 
                 'Management Console'}
              </p>
            </div>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <LuxuryThemeToggle />

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-platinum-600 dark:text-platinum-400 hover:text-champagne-400 transition-colors"
            >
              <Bell className="w-5 h-5" />
            </motion.button>
            
            <div className="flex items-center space-x-3">
              <img
                src={user.avatar}
                alt={user.name}
                className="w-10 h-10 rounded-full border-2 border-champagne-400"
              />
              <div className="hidden md:block">
                <p className="text-sm font-medium text-platinum-900 dark:text-platinum-50">{user.name}</p>
                <p className="text-xs text-champagne-400 capitalize">{user.role}</p>
              </div>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={logout}
              className="text-platinum-600 dark:text-platinum-400 hover:text-rose-400"
            >
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </motion.header>
  );
}