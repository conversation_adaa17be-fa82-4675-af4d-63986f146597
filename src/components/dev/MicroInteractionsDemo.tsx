import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Crown, Heart, Star, Sparkles, Mail, Lock } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';
import { InteractiveCard, HoverReveal, MagneticButton, FloatingElement } from '../ui/InteractiveCard';
import { LoadingSpinner, LoadingDots, LoadingSkeleton } from '../ui/Loading';
import { Toast, ConfirmationAnimation, PulseIndicator, ProgressRing } from '../ui/Feedback';
import { SectionTransition, StaggerContainer, StaggerItem } from '../ui/PageTransition';

export function MicroInteractionsDemo() {
  const [showToast, setShowToast] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [progress, setProgress] = useState(65);

  const handleButtonClick = () => {
    setShowConfirmation(true);
  };

  const handleToastClick = () => {
    setShowToast(true);
  };

  return (
    <div className="min-h-screen bg-luxury-gradient p-8">
      <div className="max-w-6xl mx-auto space-y-16">
        {/* Header */}
        <SectionTransition>
          <div className="text-center space-y-4">
            <FloatingElement intensity="subtle">
              <Crown className="w-16 h-16 text-champagne-400 mx-auto" />
            </FloatingElement>
            <h1 className="text-display-lg font-serif font-bold text-platinum-50">
              Micro-Interactions Showcase
            </h1>
            <p className="text-body-xl text-platinum-300 max-w-2xl mx-auto">
              Experience the sophisticated animations and interactions that elevate the Opulence platform
            </p>
          </div>
        </SectionTransition>

        {/* Enhanced Buttons */}
        <SectionTransition delay={0.2}>
          <div className="space-y-8">
            <h2 className="text-display-md font-serif font-bold text-platinum-50 text-center">
              Enhanced Buttons
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="p-6 space-y-4">
                <h3 className="text-heading-lg font-semibold text-platinum-50">Standard Variants</h3>
                <div className="space-y-3">
                  <Button variant="primary">Primary Button</Button>
                  <Button variant="secondary">Secondary Button</Button>
                  <Button variant="outline">Outline Button</Button>
                  <Button variant="ghost">Ghost Button</Button>
                </div>
              </Card>

              <Card className="p-6 space-y-4">
                <h3 className="text-heading-lg font-semibold text-platinum-50">Enhanced Effects</h3>
                <div className="space-y-3">
                  <Button variant="luxury" glow>Luxury with Glow</Button>
                  <Button variant="primary" magnetic>Magnetic Button</Button>
                  <Button variant="secondary" loading>Loading State</Button>
                  <MagneticButton onClick={handleButtonClick}>
                    Magnetic Custom
                  </MagneticButton>
                </div>
              </Card>

              <Card className="p-6 space-y-4">
                <h3 className="text-heading-lg font-semibold text-platinum-50">Interactive Cards</h3>
                <InteractiveCard hover tilt glow className="p-4 text-center">
                  <Star className="w-8 h-8 text-champagne-400 mx-auto mb-2" />
                  <p className="text-platinum-50">3D Tilt Card</p>
                </InteractiveCard>
              </Card>
            </div>
          </div>
        </SectionTransition>

        {/* Enhanced Inputs */}
        <SectionTransition delay={0.3}>
          <div className="space-y-8">
            <h2 className="text-display-md font-serif font-bold text-platinum-50 text-center">
              Enhanced Form Inputs
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="p-6 space-y-4">
                <Input 
                  label="Email Address" 
                  placeholder="Enter your email"
                  icon={<Mail className="w-4 h-4" />}
                />
                <Input 
                  label="Password" 
                  type="password"
                  placeholder="Enter your password"
                  icon={<Lock className="w-4 h-4" />}
                />
                <Input 
                  label="Success State" 
                  placeholder="This field is valid"
                  success
                />
                <Input 
                  label="Error State" 
                  placeholder="This field has an error"
                  error="Please enter a valid value"
                />
              </Card>

              <Card className="p-6 space-y-4">
                <h3 className="text-heading-lg font-semibold text-platinum-50 mb-4">Hover Reveal</h3>
                <HoverReveal
                  revealContent={
                    <div className="text-center">
                      <Sparkles className="w-8 h-8 text-champagne-400 mx-auto mb-2" />
                      <p className="text-platinum-50">Revealed Content!</p>
                    </div>
                  }
                  className="bg-platinum-800/50 rounded-xl p-8 text-center"
                >
                  <Heart className="w-8 h-8 text-rose-400 mx-auto mb-2" />
                  <p className="text-platinum-50">Hover to Reveal</p>
                </HoverReveal>
              </Card>
            </div>
          </div>
        </SectionTransition>

        {/* Loading States */}
        <SectionTransition delay={0.4}>
          <div className="space-y-8">
            <h2 className="text-display-md font-serif font-bold text-platinum-50 text-center">
              Luxury Loading States
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="p-6 text-center space-y-4">
                <h3 className="text-heading-lg font-semibold text-platinum-50">Spinners</h3>
                <div className="space-y-4">
                  <LoadingSpinner variant="luxury" size="lg" />
                  <LoadingSpinner variant="elegant" size="md" />
                  <LoadingSpinner variant="minimal" size="sm" />
                </div>
              </Card>

              <Card className="p-6 text-center space-y-4">
                <h3 className="text-heading-lg font-semibold text-platinum-50">Dots & Progress</h3>
                <div className="space-y-6">
                  <LoadingDots size="lg" />
                  <ProgressRing progress={progress} size={80} />
                  <div className="flex items-center space-x-2">
                    <PulseIndicator isActive color="champagne" />
                    <span className="text-platinum-300">Active Status</span>
                  </div>
                </div>
              </Card>

              <Card className="p-6 space-y-4">
                <h3 className="text-heading-lg font-semibold text-platinum-50">Skeletons</h3>
                <LoadingSkeleton variant="card" />
              </Card>
            </div>
          </div>
        </SectionTransition>

        {/* Staggered Animation */}
        <SectionTransition delay={0.5}>
          <div className="space-y-8">
            <h2 className="text-display-md font-serif font-bold text-platinum-50 text-center">
              Staggered Animations
            </h2>
            <StaggerContainer className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
                <StaggerItem key={item}>
                  <Card hover className="p-6 text-center">
                    <div className="w-12 h-12 bg-champagne-gradient rounded-xl mx-auto mb-3 flex items-center justify-center">
                      <span className="text-platinum-900 font-bold">{item}</span>
                    </div>
                    <p className="text-platinum-50">Item {item}</p>
                  </Card>
                </StaggerItem>
              ))}
            </StaggerContainer>
          </div>
        </SectionTransition>

        {/* Feedback Actions */}
        <SectionTransition delay={0.6}>
          <div className="space-y-8">
            <h2 className="text-display-md font-serif font-bold text-platinum-50 text-center">
              Feedback & Confirmations
            </h2>
            <div className="flex justify-center space-x-4">
              <Button onClick={handleToastClick}>Show Toast</Button>
              <Button onClick={handleButtonClick} variant="luxury">
                Show Confirmation
              </Button>
            </div>
          </div>
        </SectionTransition>
      </div>

      {/* Toast Notification */}
      <Toast
        type="luxury"
        title="Exquisite!"
        message="Your luxury experience has been enhanced"
        isVisible={showToast}
        onClose={() => setShowToast(false)}
      />

      {/* Confirmation Animation */}
      <ConfirmationAnimation
        isVisible={showConfirmation}
        variant="luxury"
        onComplete={() => setShowConfirmation(false)}
      />
    </div>
  );
}