import React, { useState } from 'react';
import { Wifi, WifiOff, Zap, Clock, AlertTriangle } from 'lucide-react';
import { useNetworkStatus, useNetworkThrottling } from '../../hooks/useNetworkStatus';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';

export function NetworkTester() {
  const networkStatus = useNetworkStatus();
  const { isThrottled, throttleType, enableThrottling, disableThrottling } = useNetworkThrottling();
  const [testResults, setTestResults] = useState<string[]>([]);

  const runLoadingTest = async () => {
    const startTime = Date.now();
    setTestResults(prev => [...prev, `Starting loading test at ${new Date().toLocaleTimeString()}`]);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      const duration = Date.now() - startTime;
      setTestResults(prev => [...prev, `✅ Test completed in ${duration}ms`]);
    } catch (error) {
      setTestResults(prev => [...prev, `❌ Test failed: ${error}`]);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="p-4 w-80 bg-platinum-900/95 backdrop-blur-sm border-champagne-500/20">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-platinum-50 mb-2 flex items-center">
            {networkStatus.isOnline ? (
              <Wifi className="w-5 h-5 mr-2 text-green-400" />
            ) : (
              <WifiOff className="w-5 h-5 mr-2 text-red-400" />
            )}
            Network Tester
          </h3>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-platinum-400">Status:</span>
              <span className={networkStatus.isOnline ? 'text-green-400' : 'text-red-400'}>
                {networkStatus.isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-platinum-400">Connection:</span>
              <span className="text-platinum-300">{networkStatus.effectiveType}</span>
            </div>
            
            {networkStatus.isSlowConnection && (
              <div className="flex items-center text-yellow-400">
                <Clock className="w-4 h-4 mr-1" />
                <span className="text-xs">Slow connection detected</span>
              </div>
            )}
          </div>
        </div>

        <div className="mb-4">
          <h4 className="text-sm font-medium text-platinum-50 mb-2">Network Throttling</h4>
          <div className="grid grid-cols-2 gap-2">
            <Button
              size="sm"
              variant={isThrottled && throttleType === 'slow-3g' ? 'primary' : 'outline'}
              onClick={() => enableThrottling('slow-3g')}
              className="text-xs"
            >
              <Clock className="w-3 h-3 mr-1" />
              Slow 3G
            </Button>
            
            <Button
              size="sm"
              variant={isThrottled && throttleType === 'fast-3g' ? 'primary' : 'outline'}
              onClick={() => enableThrottling('fast-3g')}
              className="text-xs"
            >
              <Zap className="w-3 h-3 mr-1" />
              Fast 3G
            </Button>
            
            <Button
              size="sm"
              variant={isThrottled && throttleType === 'offline' ? 'primary' : 'outline'}
              onClick={() => enableThrottling('offline')}
              className="text-xs"
            >
              <WifiOff className="w-3 h-3 mr-1" />
              Offline
            </Button>
            
            <Button
              size="sm"
              variant={!isThrottled ? 'primary' : 'outline'}
              onClick={disableThrottling}
              className="text-xs"
            >
              Normal
            </Button>
          </div>
        </div>

        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-sm font-medium text-platinum-50">Loading Tests</h4>
            <Button size="sm" variant="ghost" onClick={clearResults} className="text-xs">
              Clear
            </Button>
          </div>
          
          <Button
            size="sm"
            onClick={runLoadingTest}
            className="w-full mb-2"
          >
            Test Loading States
          </Button>
          
          {testResults.length > 0 && (
            <div className="bg-platinum-800/50 rounded-lg p-2 max-h-32 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="text-xs text-platinum-300 mb-1">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>

        {isThrottled && (
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-2">
            <div className="flex items-center text-yellow-400 text-xs">
              <AlertTriangle className="w-3 h-3 mr-1" />
              Network throttling active: {throttleType}
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}