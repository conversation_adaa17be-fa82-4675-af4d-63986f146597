import React from 'react';
import { motion } from 'framer-motion';
import { 
  DisplayText, 
  Heading, 
  BodyText, 
  PriceDisplay, 
  Caption, 
  Emphasis, 
  StatusBadge, 
  Metadata,
  ResponsiveText 
} from '../ui/Typography';
import { 
  Section, 
  ContentContainer, 
  Separator, 
  LuxuryGrid, 
  LuxuryFlex, 
  HierarchyCard, 
  InfoHierarchy, 
  Whitespace 
} from '../ui/Layout';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { formatCurrency, formatLuxuryNumber, formatPriceRange, formatDuration } from '../../lib/utils';

export function TypographyShowcase() {
  return (
    <div className="min-h-screen bg-luxury-gradient">
      <div className="max-w-7xl mx-auto px-6 py-12">
        
        {/* Header */}
        <Section spacing="luxury">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <DisplayText size="2xl" className="mb-6">
              Typography & Visual Hierarchy
            </DisplayText>
            <BodyText size="xl" className="max-w-3xl mx-auto">
              Enhanced typography system for ultra-luxury travel experiences with sophisticated visual hierarchy and strategic content density.
            </BodyText>
          </motion.div>
        </Section>

        {/* Display Text Showcase */}
        <Section spacing="relaxed">
          <HierarchyCard hierarchy="primary" padding="luxury">
            <Heading size="xl" className="mb-8">Display Typography</Heading>
            <div className="space-y-8">
              <div>
                <Caption size="md" className="mb-3">Display 2XL</Caption>
                <DisplayText size="2xl">Luxury Travel Redefined</DisplayText>
              </div>
              <div>
                <Caption size="md" className="mb-3">Display XL</Caption>
                <DisplayText size="xl">Exclusive Morocco Experience</DisplayText>
              </div>
              <div>
                <Caption size="md" className="mb-3">Display LG</Caption>
                <DisplayText size="lg">Imperial Cities Journey</DisplayText>
              </div>
              <div>
                <Caption size="md" className="mb-3">Display MD</Caption>
                <DisplayText size="md">Marrakech to Casablanca</DisplayText>
              </div>
              <div>
                <Caption size="md" className="mb-3">Display SM</Caption>
                <DisplayText size="sm">Private Villa Collection</DisplayText>
              </div>
            </div>
          </HierarchyCard>
        </Section>

        {/* Price Display Showcase */}
        <Section spacing="relaxed">
          <LuxuryGrid columns={2} gap="xl">
            <HierarchyCard hierarchy="primary" padding="luxury">
              <Heading size="xl" className="mb-8">Price Typography</Heading>
              <div className="space-y-8">
                <div>
                  <Caption size="md" className="mb-3">Hero Price</Caption>
                  <PriceDisplay amount={125000} size="hero" prefix="From " />
                </div>
                <div>
                  <Caption size="md" className="mb-3">Large Price</Caption>
                  <PriceDisplay amount={85000} size="lg" style="elegant" />
                </div>
                <div>
                  <Caption size="md" className="mb-3">Medium Price</Caption>
                  <PriceDisplay amount={45000} size="md" style="compact" />
                </div>
                <div>
                  <Caption size="md" className="mb-3">Small Price</Caption>
                  <PriceDisplay amount={12500} size="sm" showCents />
                </div>
                <div>
                  <Caption size="md" className="mb-3">Price Range</Caption>
                  <BodyText>{formatPriceRange(75000, 150000)}</BodyText>
                </div>
              </div>
            </HierarchyCard>

            <HierarchyCard hierarchy="primary" padding="luxury">
              <Heading size="xl" className="mb-8">Luxury Formatting</Heading>
              <div className="space-y-6">
                <Metadata 
                  label="Guest Count" 
                  value={formatLuxuryNumber(8, { style: 'full' })} 
                />
                <Metadata 
                  label="Duration" 
                  value={formatDuration(14)} 
                />
                <Metadata 
                  label="Experiences" 
                  value={formatLuxuryNumber(47, { style: 'compact' })} 
                />
                <Metadata 
                  label="Partner Rating" 
                  value={formatLuxuryNumber(9.8, { precision: 1 })} 
                />
                <Separator variant="luxury" spacing="md" />
                <div className="space-y-3">
                  <Caption size="md">Status Examples</Caption>
                  <LuxuryFlex gap="md" wrap>
                    <StatusBadge status="confirmed" variant="luxury" />
                    <StatusBadge status="proposed" variant="premium" />
                    <StatusBadge status="pending" variant="standard" />
                  </LuxuryFlex>
                </div>
              </div>
            </HierarchyCard>
          </LuxuryGrid>
        </Section>

        {/* Content Hierarchy Showcase */}
        <Section spacing="relaxed">
          <HierarchyCard hierarchy="primary" padding="luxury">
            <Heading size="xl" className="mb-8">Content Hierarchy</Heading>
            <LuxuryGrid columns={3} gap="lg">
              <div>
                <InfoHierarchy
                  primary="Royal Atlas Mountains"
                  secondary="Private helicopter tour with champagne service"
                  tertiary="Duration: 4 hours • Max 6 guests"
                  metadata={
                    <div className="space-y-2">
                      <PriceDisplay amount={8500} size="md" />
                      <StatusBadge status="available" variant="luxury" />
                    </div>
                  }
                />
              </div>
              <div>
                <InfoHierarchy
                  primary="La Mamounia Palace"
                  secondary="Winston Churchill's favorite suite with private butler"
                  tertiary="Marrakech • 5-star luxury"
                  metadata={
                    <div className="space-y-2">
                      <PriceDisplay amount={3200} size="md" suffix="/night" />
                      <StatusBadge status="limited" variant="premium" />
                    </div>
                  }
                />
              </div>
              <div>
                <InfoHierarchy
                  primary="Sahara Desert Camp"
                  secondary="Exclusive glamping under the stars"
                  tertiary="Merzouga • Ultra-luxury tents"
                  metadata={
                    <div className="space-y-2">
                      <PriceDisplay amount={1800} size="md" suffix="/night" />
                      <StatusBadge status="exclusive" variant="luxury" />
                    </div>
                  }
                />
              </div>
            </LuxuryGrid>
          </HierarchyCard>
        </Section>

        {/* Emphasis and Special Text */}
        <Section spacing="relaxed">
          <HierarchyCard hierarchy="primary" padding="luxury">
            <Heading size="xl" className="mb-8">Text Emphasis</Heading>
            <ContentContainer spacing="relaxed">
              <BodyText size="lg">
                Experience Morocco like never before with our <Emphasis variant="luxury">ultra-luxury</Emphasis> travel 
                experiences. Each journey is <Emphasis variant="premium">meticulously crafted</Emphasis> for the most 
                discerning travelers, featuring <Emphasis variant="exclusive">exclusive access</Emphasis> to Morocco's 
                hidden gems and <Emphasis variant="vip">VIP treatment</Emphasis> throughout your stay.
              </BodyText>
              
              <Whitespace size="lg" />
              
              <div className="grid grid-cols-2 gap-8">
                <div>
                  <Caption size="lg" className="mb-4">Heading Hierarchy</Caption>
                  <div className="space-y-4">
                    <Heading size="xl">Extra Large Heading</Heading>
                    <Heading size="lg">Large Heading</Heading>
                    <Heading size="md">Medium Heading</Heading>
                    <Heading size="sm">Small Heading</Heading>
                  </div>
                </div>
                <div>
                  <Caption size="lg" className="mb-4">Body Text Sizes</Caption>
                  <div className="space-y-4">
                    <BodyText size="xl">Extra large body text for important content</BodyText>
                    <BodyText size="lg">Large body text for standard content</BodyText>
                    <BodyText size="md">Medium body text for secondary content</BodyText>
                    <BodyText size="sm">Small body text for captions and metadata</BodyText>
                  </div>
                </div>
              </div>
            </ContentContainer>
          </HierarchyCard>
        </Section>

        {/* Responsive Typography */}
        <Section spacing="relaxed">
          <HierarchyCard hierarchy="primary" padding="luxury">
            <Heading size="xl" className="mb-8">Responsive Typography</Heading>
            <div className="space-y-8">
              <ResponsiveText
                mobile="text-display-sm"
                tablet="text-display-md"
                desktop="text-display-lg"
                className="font-serif font-bold text-platinum-50 text-balance"
              >
                This heading scales beautifully across all devices
              </ResponsiveText>
              
              <ResponsiveText
                mobile="text-body-md"
                tablet="text-body-lg"
                desktop="text-body-xl"
                className="text-platinum-300 max-w-4xl"
              >
                Responsive body text that maintains readability and luxury aesthetic 
                across mobile, tablet, and desktop viewports while preserving the 
                sophisticated typography hierarchy.
              </ResponsiveText>
            </div>
          </HierarchyCard>
        </Section>

        {/* Action Section */}
        <Section spacing="luxury">
          <div className="text-center">
            <DisplayText size="md" className="mb-6">
              Ready to Experience Luxury?
            </DisplayText>
            <BodyText size="lg" className="mb-8 max-w-2xl mx-auto">
              Discover how enhanced typography elevates the entire user experience
            </BodyText>
            <LuxuryFlex justify="center" gap="lg">
              <Button size="lg">Explore Morocco</Button>
              <Button variant="outline" size="lg">View Portfolio</Button>
            </LuxuryFlex>
          </div>
        </Section>

      </div>
    </div>
  );
}