import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  LuxuryTripCard, 
  AccommodationGalleryCard, 
  MagazineActivityCard,
  MasonryLayout,
  AdaptiveGrid 
} from '../ui/LuxuryCards';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Section, ContentContainer } from '../ui/Layout';
import { Heading, BodyText } from '../ui/Typography';
import { useTrips } from '../../hooks/api/useTrips';
import { useAuthState } from '../../hooks/useAuth';

export function LuxuryCardsShowcase() {
  const { user } = useAuthState();
  const { data: trips = [] } = useTrips(user?.id);
  const [selectedLayout, setSelectedLayout] = useState<'grid' | 'masonry'>('grid');

  // Mock data for demonstration
  const mockAccommodations = trips[0]?.itinerary[0]?.accommodation ? [trips[0].itinerary[0].accommodation] : [];
  const mockActivities = trips[0]?.itinerary[0]?.activities || [];

  const handleTripSelect = (trip: any) => {
    console.log('Selected trip:', trip.title);
  };

  const handleAccommodationSelect = (accommodation: any) => {
    console.log('Selected accommodation:', accommodation.name);
  };

  const handleActivitySelect = (activity: any) => {
    console.log('Selected activity:', activity.title);
  };

  const handleAskQuestion = (activityId: string, context: string) => {
    console.log('Ask question about:', context);
  };

  const handleFavorite = (tripId: string) => {
    console.log('Favorited trip:', tripId);
  };

  const handleShare = (tripId: string) => {
    console.log('Shared trip:', tripId);
  };

  return (
    <div className="min-h-screen bg-luxury-gradient">
      <ContentContainer className="py-12">
        {/* Header */}
        <Section className="mb-16">
          <div className="text-center mb-12">
            <Heading size="2xl" className="mb-6">
              Luxury Card Designs Showcase
            </Heading>
            <BodyText size="lg" className="text-platinum-300 max-w-3xl mx-auto">
              Experience our sophisticated card components designed for ultra-luxury travel experiences.
              Each card features enhanced image treatment, smooth transitions, and magazine-style layouts.
            </BodyText>
          </div>

          {/* Layout Toggle */}
          <div className="flex justify-center mb-8">
            <div className="bg-platinum-900/60 backdrop-blur-sm border border-platinum-700/30 rounded-xl p-1">
              <Button
                variant={selectedLayout === 'grid' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setSelectedLayout('grid')}
                className="mr-1"
              >
                Adaptive Grid
              </Button>
              <Button
                variant={selectedLayout === 'masonry' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setSelectedLayout('masonry')}
              >
                Masonry Layout
              </Button>
            </div>
          </div>
        </Section>

        {/* Trip Cards Section */}
        <Section className="mb-16">
          <Heading size="xl" className="mb-8">
            Sophisticated Trip Cards
          </Heading>
          <BodyText className="text-platinum-300 mb-8">
            Enhanced trip cards with sophisticated image overlays, progressive disclosure, and luxury interactions.
          </BodyText>

          {selectedLayout === 'grid' ? (
            <AdaptiveGrid minItemWidth={380} gap="lg">
              {trips.slice(0, 6).map((trip, index) => (
                <LuxuryTripCard
                  key={trip.id}
                  trip={trip}
                  variant={index === 0 ? 'featured' : index % 3 === 0 ? 'compact' : 'standard'}
                  onSelect={handleTripSelect}
                  onFavorite={handleFavorite}
                  onShare={handleShare}
                />
              ))}
            </AdaptiveGrid>
          ) : (
            <MasonryLayout columns={{ sm: 1, md: 2, lg: 3, xl: 4 }} gap="lg">
              {trips.slice(0, 6).map((trip, index) => (
                <LuxuryTripCard
                  key={trip.id}
                  trip={trip}
                  variant={index === 0 ? 'featured' : index % 3 === 0 ? 'compact' : 'standard'}
                  onSelect={handleTripSelect}
                  onFavorite={handleFavorite}
                  onShare={handleShare}
                />
              ))}
            </MasonryLayout>
          )}
        </Section>

        {/* Accommodation Gallery Section */}
        {mockAccommodations.length > 0 && (
          <Section className="mb-16">
            <Heading size="xl" className="mb-8">
              Gallery-Style Accommodation Cards
            </Heading>
            <BodyText className="text-platinum-300 mb-8">
              Gallery-style layouts with smooth image transitions and elegant content presentation.
            </BodyText>

            <AdaptiveGrid minItemWidth={320} gap="lg">
              {/* Duplicate accommodations for demo */}
              {Array.from({ length: 6 }).map((_, index) => (
                <AccommodationGalleryCard
                  key={index}
                  accommodation={mockAccommodations[0]}
                  onSelect={handleAccommodationSelect}
                />
              ))}
            </AdaptiveGrid>
          </Section>
        )}

        {/* Magazine-Style Activity Cards */}
        {mockActivities.length > 0 && (
          <Section className="mb-16">
            <Heading size="xl" className="mb-8">
              Magazine-Style Activity Cards
            </Heading>
            <BodyText className="text-platinum-300 mb-8">
              Asymmetric compositions with sophisticated layouts inspired by luxury travel magazines.
            </BodyText>

            <div className="space-y-8">
              {mockActivities.slice(0, 4).map((activity, index) => (
                <MagazineActivityCard
                  key={activity.id}
                  activity={activity}
                  layout={index % 3 === 0 ? 'full' : index % 2 === 0 ? 'left' : 'right'}
                  onSelect={handleActivitySelect}
                  onAskQuestion={handleAskQuestion}
                />
              ))}
            </div>
          </Section>
        )}

        {/* Design Features */}
        <Section>
          <Heading size="xl" className="mb-8">
            Design Features
          </Heading>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                title: 'Enhanced Image Treatment',
                description: 'Sophisticated overlays, gradient masks, and smooth hover transitions create visual depth and luxury appeal.',
                features: ['Gradient overlays', 'Hover scale effects', 'Progressive image loading', 'Smart aspect ratios']
              },
              {
                title: 'Gallery-Style Layouts',
                description: 'Smooth image transitions and elegant content organization for accommodation browsing.',
                features: ['Image navigation', 'Smooth transitions', 'Content adaptation', 'Touch optimization']
              },
              {
                title: 'Magazine Compositions',
                description: 'Asymmetric layouts with sophisticated typography and content hierarchy.',
                features: ['Asymmetric design', 'Progressive disclosure', 'Content expansion', 'Visual hierarchy']
              },
              {
                title: 'Responsive Layouts',
                description: 'Adaptive grids and masonry layouts that respond intelligently to content and screen size.',
                features: ['Auto-fit grids', 'Masonry columns', 'Content-aware sizing', 'Mobile optimization']
              },
              {
                title: 'Luxury Interactions',
                description: 'Sophisticated micro-interactions with premium animations and feedback.',
                features: ['Tilt effects', 'Glow animations', 'Magnetic buttons', 'Smooth transitions']
              },
              {
                title: 'VVIP Experience',
                description: 'Every detail crafted for ultra-luxury clientele with premium aesthetics.',
                features: ['Platinum theme', 'Champagne accents', 'Premium typography', 'Luxury spacing']
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card hover className="h-full p-6">
                  <Heading size="lg" className="mb-4">
                    {feature.title}
                  </Heading>
                  <BodyText className="text-platinum-300 mb-4">
                    {feature.description}
                  </BodyText>
                  <ul className="space-y-2">
                    {feature.features.map((item, i) => (
                      <li key={i} className="flex items-center text-sm text-platinum-400">
                        <div className="w-1.5 h-1.5 bg-champagne-400 rounded-full mr-3" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </Card>
              </motion.div>
            ))}
          </div>
        </Section>
      </ContentContainer>
    </div>
  );
}
