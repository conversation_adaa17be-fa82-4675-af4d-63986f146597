import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { 
  Home, 
  User, 
  Settings, 
  Shield, 
  Users, 
  MessageSquare, 
  Calendar, 
  FileText, 
  Map, 
  Crown,
  Plane,
  Hotel,
  Camera,
  Clock,
  Star
} from 'lucide-react';

interface RouteInfo {
  path: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  category: 'auth' | 'client' | 'admin' | 'partner' | 'shared';
  status: 'complete' | 'partial' | 'planned';
  userRole?: 'vvip' | 'admin' | 'partner' | 'any';
}

const routes: RouteInfo[] = [
  // Authentication Routes
  {
    path: '/login',
    name: 'Login Page',
    description: 'Authentication page with role-based login (VVIP, Admin, Partner)',
    icon: Shield,
    category: 'auth',
    status: 'complete',
    userRole: 'any'
  },

  // Client/VVIP Routes
  {
    path: '/client',
    name: 'Client Dashboard',
    description: 'VVIP client main dashboard with trip overview and statistics',
    icon: Crown,
    category: 'client',
    status: 'complete',
    userRole: 'vvip'
  },
  {
    path: '/client/trip/1',
    name: 'Trip Timeline',
    description: 'Interactive day-by-day trip timeline with activities and accommodations',
    icon: Calendar,
    category: 'client',
    status: 'complete',
    userRole: 'vvip'
  },
  {
    path: '/client/chat',
    name: 'AI Concierge Chat',
    description: 'AI-powered chat interface for trip assistance and recommendations',
    icon: MessageSquare,
    category: 'client',
    status: 'complete',
    userRole: 'vvip'
  },

  // Admin Routes
  {
    path: '/admin',
    name: 'Admin Dashboard',
    description: 'Travel management console with analytics, quotes, and partner oversight',
    icon: Settings,
    category: 'admin',
    status: 'complete',
    userRole: 'admin'
  },
  {
    path: '/admin/quote-builder',
    name: 'Quote Builder',
    description: 'Create and customize luxury travel itineraries and quotes',
    icon: FileText,
    category: 'admin',
    status: 'complete',
    userRole: 'admin'
  },

  // Partner Routes
  {
    path: '/partner',
    name: 'Partner Dashboard',
    description: 'Service provider portal for managing requests and offerings',
    icon: Users,
    category: 'partner',
    status: 'complete',
    userRole: 'partner'
  },

  // Shared/Utility Routes
  {
    path: '/',
    name: 'Home/Landing',
    description: 'Main landing page with role-based routing',
    icon: Home,
    category: 'shared',
    status: 'complete',
    userRole: 'any'
  },
  {
    path: '/typography-showcase',
    name: 'Typography Showcase',
    description: 'Enhanced typography system demonstration with luxury formatting',
    icon: FileText,
    category: 'shared',
    status: 'complete',
    userRole: 'any'
  },
  {
    path: '/micro-interactions-demo',
    name: 'Micro-Interactions Demo',
    description: 'Sophisticated animations and micro-interactions showcase',
    icon: Star,
    category: 'shared',
    status: 'complete',
    userRole: 'any'
  }
];

const categoryColors = {
  auth: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
  client: 'bg-champagne-500/20 text-champagne-400 border-champagne-500/30',
  admin: 'bg-rose-500/20 text-rose-400 border-rose-500/30',
  partner: 'bg-green-500/20 text-green-400 border-green-500/30',
  shared: 'bg-platinum-600/20 text-platinum-300 border-platinum-600/30'
};

const statusColors = {
  complete: 'bg-green-500/20 text-green-400',
  partial: 'bg-champagne-500/20 text-champagne-400',
  planned: 'bg-platinum-600/20 text-platinum-400'
};

const statusIcons = {
  complete: '✅',
  partial: '🚧',
  planned: '📋'
};

export function RoutesIndex() {
  const groupedRoutes = routes.reduce((acc, route) => {
    if (!acc[route.category]) {
      acc[route.category] = [];
    }
    acc[route.category].push(route);
    return acc;
  }, {} as Record<string, RouteInfo[]>);

  const categoryTitles = {
    auth: 'Authentication',
    client: 'VVIP Client Pages',
    admin: 'Admin Management',
    partner: 'Partner Portal',
    shared: 'Shared/Utility'
  };

  const demoAccounts = [
    { role: 'VVIP Client', email: '<EMAIL>', password: 'password', description: 'Ultra-luxury travel client' },
    { role: 'Admin', email: '<EMAIL>', password: 'password', description: 'Travel management console' },
    { role: 'Partner', email: '<EMAIL>', password: 'password', description: 'Service provider portal' }
  ];

  return (
    <div className="min-h-screen bg-luxury-gradient">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-champagne-gradient rounded-xl flex items-center justify-center">
              <Map className="w-6 h-6 text-platinum-900" />
            </div>
            <div>
              <h1 className="text-4xl font-serif font-bold text-platinum-50">Opulence Routes</h1>
              <p className="text-platinum-300">Development & Testing Navigation</p>
            </div>
          </div>
          
          <div className="bg-platinum-900/50 rounded-xl p-4 border border-champagne-500/20">
            <p className="text-platinum-300 text-sm">
              🚀 <strong>Development Tool:</strong> This page provides easy access to all app routes for testing and development. 
              Use the demo accounts below to test different user roles and experiences.
            </p>
          </div>
        </div>

        {/* Demo Accounts */}
        <Card className="mb-8">
          <h2 className="text-2xl font-serif font-bold text-platinum-50 mb-4">Demo Accounts</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {demoAccounts.map((account, index) => (
              <div key={index} className="bg-platinum-800/30 rounded-xl p-4">
                <h3 className="font-semibold text-platinum-50 mb-2">{account.role}</h3>
                <p className="text-sm text-platinum-300 mb-3">{account.description}</p>
                <div className="space-y-1 text-xs">
                  <div className="text-platinum-400">
                    <strong>Email:</strong> <span className="text-champagne-400">{account.email}</span>
                  </div>
                  <div className="text-platinum-400">
                    <strong>Password:</strong> <span className="text-champagne-400">{account.password}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Routes by Category */}
        {Object.entries(groupedRoutes).map(([category, categoryRoutes]) => (
          <div key={category} className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium border ${categoryColors[category as keyof typeof categoryColors]}`}>
                {categoryTitles[category as keyof typeof categoryTitles]}
              </span>
              <span className="text-platinum-400 text-sm">
                {categoryRoutes.length} page{categoryRoutes.length !== 1 ? 's' : ''}
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categoryRoutes.map((route, index) => {
                const IconComponent = route.icon;
                return (
                  <Card key={index} className="p-6 hover:border-champagne-500/40 transition-all duration-300">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-champagne-500/20 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-5 h-5 text-champagne-400" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-platinum-50">{route.name}</h3>
                          {route.userRole && route.userRole !== 'any' && (
                            <span className="text-xs text-champagne-400 capitalize">
                              {route.userRole} only
                            </span>
                          )}
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[route.status]}`}>
                        {statusIcons[route.status]} {route.status}
                      </span>
                    </div>

                    <p className="text-platinum-300 text-sm mb-4 line-clamp-3">
                      {route.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <code className="text-xs bg-platinum-800/50 px-2 py-1 rounded text-champagne-400">
                        {route.path}
                      </code>
                      <Link to={route.path}>
                        <Button variant="outline" size="sm">
                          Visit Page
                        </Button>
                      </Link>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        ))}

        {/* Development Info */}
        <Card className="mt-8">
          <h2 className="text-2xl font-serif font-bold text-platinum-50 mb-4">Development Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-platinum-50 mb-3">🎨 Design System</h3>
              <ul className="space-y-2 text-sm text-platinum-300">
                <li>• <strong>Colors:</strong> Platinum, Champagne, Rose Gold palette</li>
                <li>• <strong>Typography:</strong> Playfair Display (serif) + Inter (sans-serif)</li>
                <li>• <strong>Components:</strong> Framer Motion animations</li>
                <li>• <strong>Theme:</strong> Full dark luxury theme</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-platinum-50 mb-3">🇲🇦 Content</h3>
              <ul className="space-y-2 text-sm text-platinum-300">
                <li>• <strong>Location:</strong> 100% Moroccan luxury experiences</li>
                <li>• <strong>Partners:</strong> Royal Mansour, La Mamounia, Heli Atlas</li>
                <li>• <strong>Activities:</strong> Atlas Mountains, Sahara Desert, Marrakech</li>
                <li>• <strong>Pricing:</strong> Ultra-luxury VVIP market positioning</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* Quick Actions */}
        <div className="mt-8 flex flex-wrap gap-4">
          <Link to="/login">
            <Button variant="primary">
              <Shield className="w-4 h-4 mr-2" />
              Start Testing (Login)
            </Button>
          </Link>
          <Link to="/client">
            <Button variant="secondary">
              <Crown className="w-4 h-4 mr-2" />
              VVIP Experience
            </Button>
          </Link>
          <Link to="/admin">
            <Button variant="secondary">
              <Settings className="w-4 h-4 mr-2" />
              Admin Console
            </Button>
          </Link>
          <Link to="/partner">
            <Button variant="secondary">
              <Users className="w-4 h-4 mr-2" />
              Partner Portal
            </Button>
          </Link>
        </div>

        {/* Footer */}
        <div className="mt-12 pt-8 border-t border-champagne-500/20">
          <div className="flex items-center justify-between">
            <div className="text-sm text-platinum-400">
              <p>Opulence Ultra-Luxury Travel Platform</p>
              <p>Development Routes Index • Morocco Content • Dark Luxury Theme</p>
            </div>
            <div className="text-sm text-platinum-400 text-right">
              <p>🇲🇦 Moroccan Luxury Experiences</p>
              <p>✨ VVIP Market Positioning</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}