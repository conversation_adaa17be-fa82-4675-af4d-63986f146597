import React from 'react';
import { cn } from '../../lib/utils';
import { typography, createTypographyClass, responsiveTypography } from '../../lib/typography';

// Display Text Component
interface DisplayTextProps {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'div';
}

export function DisplayText({ 
  size = 'lg', 
  children, 
  className, 
  as: Component = 'h1' 
}: DisplayTextProps) {
  return (
    <Component className={cn(typography.display[size], className)}>
      {children}
    </Component>
  );
}

// Heading Component
interface HeadingProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export function Heading({ 
  size = 'lg', 
  children, 
  className, 
  as: Component = 'h2' 
}: HeadingProps) {
  return (
    <Component className={cn(typography.heading[size], className)}>
      {children}
    </Component>
  );
}

// Body Text Component
interface BodyTextProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
  className?: string;
  as?: 'p' | 'div' | 'span';
}

export function BodyText({ 
  size = 'lg', 
  children, 
  className, 
  as: Component = 'p' 
}: BodyTextProps) {
  return (
    <Component className={cn(typography.body[size], className)}>
      {children}
    </Component>
  );
}

// Price Display Component
interface PriceDisplayProps {
  amount: number;
  size?: 'sm' | 'md' | 'lg' | 'hero';
  prefix?: string;
  suffix?: string;
  className?: string;
  showCents?: boolean;
  style?: 'compact' | 'full' | 'elegant';
}

export function PriceDisplay({ 
  amount, 
  size = 'md', 
  prefix = '', 
  suffix = '', 
  className,
  showCents = false,
  style = 'elegant'
}: PriceDisplayProps) {
  const formatCurrency = (amount: number) => {
    const baseOptions: Intl.NumberFormatOptions = {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: showCents ? 2 : 0,
      maximumFractionDigits: showCents ? 2 : 0,
    };

    switch (style) {
      case 'compact':
        return new Intl.NumberFormat('en-US', {
          ...baseOptions,
          notation: 'compact',
          compactDisplay: 'short',
        }).format(amount);
      
      case 'full':
        return new Intl.NumberFormat('en-US', baseOptions).format(amount);
      
      case 'elegant':
      default:
        const formatted = new Intl.NumberFormat('en-US', baseOptions).format(amount);
        return formatted.replace(/,/g, '\u2009'); // Thin space for elegance
    }
  };

  return (
    <span className={cn(typography.price[size], className)}>
      {prefix}
      {formatCurrency(amount)}
      {suffix}
    </span>
  );
}

// Caption Component
interface CaptionProps {
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
  as?: 'span' | 'div' | 'p';
}

export function Caption({ 
  size = 'md', 
  children, 
  className, 
  as: Component = 'span' 
}: CaptionProps) {
  return (
    <Component className={cn(typography.caption[size], className)}>
      {children}
    </Component>
  );
}

// Emphasis Component
interface EmphasisProps {
  variant?: 'luxury' | 'premium' | 'exclusive' | 'vip';
  children: React.ReactNode;
  className?: string;
  as?: 'span' | 'div' | 'p';
}

export function Emphasis({ 
  variant = 'luxury', 
  children, 
  className, 
  as: Component = 'span' 
}: EmphasisProps) {
  return (
    <Component className={cn(typography.emphasis[variant], className)}>
      {children}
    </Component>
  );
}

// Status Badge Component
interface StatusBadgeProps {
  status: string;
  variant?: 'luxury' | 'premium' | 'standard';
  className?: string;
}

export function StatusBadge({ 
  status, 
  variant = 'standard', 
  className 
}: StatusBadgeProps) {
  const variants = {
    luxury: 'bg-champagne-500/20 text-champagne-400 border border-champagne-500/30',
    premium: 'bg-rose-400/20 text-rose-400 border border-rose-400/30',
    standard: 'bg-platinum-600/20 text-platinum-300 border border-platinum-600/30',
  };

  return (
    <span className={cn(
      'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
      variants[variant],
      className
    )}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
}

// Metadata Display Component
interface MetadataProps {
  label: string;
  value: string | React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
  className?: string;
}

export function Metadata({ 
  label, 
  value, 
  orientation = 'vertical', 
  className 
}: MetadataProps) {
  if (orientation === 'horizontal') {
    return (
      <div className={cn('flex items-center justify-between', className)}>
        <span className={typography.caption.md}>{label}</span>
        <span className={cn(typography.body.lg, 'text-platinum-200 font-medium')}>
          {value}
        </span>
      </div>
    );
  }

  return (
    <div className={cn('space-y-1', className)}>
      <div className={typography.caption.md}>{label}</div>
      <div className={cn(typography.body.lg, 'text-platinum-200 font-medium')}>
        {value}
      </div>
    </div>
  );
}

// Responsive Typography Wrapper
interface ResponsiveTextProps {
  mobile: string;
  tablet?: string;
  desktop?: string;
  children: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

export function ResponsiveText({ 
  mobile, 
  tablet, 
  desktop, 
  children, 
  className,
  as: Component = 'div'
}: ResponsiveTextProps) {
  const responsiveClasses = responsiveTypography(mobile, tablet, desktop);
  
  return (
    <Component className={cn(responsiveClasses, className)}>
      {children}
    </Component>
  );
}