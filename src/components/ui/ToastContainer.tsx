import React from 'react';
import { AnimatePresence } from 'framer-motion';
import { Toast } from './Feedback';
import { useToast, ToastMessage } from '../../hooks/useToast';

interface ToastContainerProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

export function ToastContainer({ position = 'top-right' }: ToastContainerProps) {
  const { toasts, removeToast } = useToast();

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
  };

  return (
    <div className={`fixed z-50 pointer-events-none ${positionClasses[position]}`}>
      <div className="flex flex-col space-y-2 max-w-sm w-screen px-4 sm:px-0 sm:w-auto">
        <AnimatePresence>
          {toasts.map((toast) => (
            <div key={toast.id} className="pointer-events-auto">
              <Toast
                type={toast.type}
                title={toast.title}
                message={toast.message}
                isVisible={true}
                onClose={() => removeToast(toast.id)}
                duration={0} // Duration handled by useToast hook
              />
            </div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}

// Toast Provider Context
import { createContext, useContext } from 'react';

const ToastContext = createContext<ReturnType<typeof useToast> | null>(null);

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const toast = useToast();

  return (
    <ToastContext.Provider value={toast}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
}

export function useToastContext() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToastContext must be used within a ToastProvider');
  }
  return context;
}