import React from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { cn } from '../../lib/utils';
import { luxuryAnimations } from '../../lib/animations';

interface InteractiveCardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  tilt?: boolean;
  glow?: boolean;
  onClick?: () => void;
  disabled?: boolean;
}

export function InteractiveCard({
  children,
  className,
  hover = true,
  tilt = false,
  glow = false,
  onClick,
  disabled = false
}: InteractiveCardProps) {
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  
  const mouseXSpring = useSpring(x);
  const mouseYSpring = useSpring(y);
  
  const rotateX = useTransform(mouseYSpring, [-0.5, 0.5], ["7.5deg", "-7.5deg"]);
  const rotateY = useTransform(mouseXSpring, [-0.5, 0.5], ["-7.5deg", "7.5deg"]);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!tilt) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    const xPct = mouseX / width - 0.5;
    const yPct = mouseY / height - 0.5;
    
    x.set(xPct);
    y.set(yPct);
  };

  const handleMouseLeave = () => {
    if (!tilt) return;
    x.set(0);
    y.set(0);
  };

  return (
    <motion.div
      className={cn(
        'rounded-2xl backdrop-blur-sm transition-all duration-300 cursor-pointer',
        'bg-white/90 dark:bg-platinum-900/50 border border-slate-200/50 dark:border-platinum-700/30',
        'hover:bg-white dark:hover:bg-platinum-900/70 hover:border-slate-300/70 dark:hover:border-platinum-600/50',
        glow && 'shadow-lg shadow-champagne-500/5',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      style={tilt ? {
        rotateX,
        rotateY,
        transformStyle: "preserve-3d"
      } : undefined}
      whileHover={hover && !disabled ? {
        y: -8,
        scale: 1.02,
        boxShadow: glow 
          ? "0 25px 50px -12px rgba(212, 175, 55, 0.25)" 
          : "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
        borderColor: "rgba(212, 175, 55, 0.3)",
        transition: {
          type: "spring",
          stiffness: 300,
          damping: 20
        }
      } : undefined}
      whileTap={!disabled ? {
        scale: 0.98,
        transition: {
          type: "spring",
          stiffness: 600,
          damping: 30
        }
      } : undefined}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={disabled ? undefined : onClick}
    >
      <div style={tilt ? { transform: "translateZ(50px)" } : undefined}>
        {children}
      </div>
    </motion.div>
  );
}

interface HoverRevealProps {
  children: React.ReactNode;
  revealContent: React.ReactNode;
  className?: string;
}

export function HoverReveal({ children, revealContent, className }: HoverRevealProps) {
  return (
    <motion.div
      className={cn('relative overflow-hidden', className)}
      whileHover="hover"
      initial="initial"
    >
      <motion.div
        variants={{
          initial: { opacity: 1, y: 0 },
          hover: { opacity: 0, y: -20 }
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        {children}
      </motion.div>
      
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        variants={{
          initial: { opacity: 0, y: 20 },
          hover: { opacity: 1, y: 0 }
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        {revealContent}
      </motion.div>
    </motion.div>
  );
}

interface MagneticButtonProps {
  children: React.ReactNode;
  className?: string;
  strength?: number;
  onClick?: () => void;
}

export function MagneticButton({ 
  children, 
  className, 
  strength = 0.3,
  onClick 
}: MagneticButtonProps) {
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  
  const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) * strength;
    const deltaY = (e.clientY - centerY) * strength;
    
    x.set(deltaX);
    y.set(deltaY);
  };

  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
  };

  return (
    <motion.button
      className={cn(
        'relative inline-flex items-center justify-center font-medium transition-all duration-200',
        'bg-champagne-gradient hover:from-champagne-600 hover:to-champagne-400',
        'text-platinum-900 shadow-lg hover:shadow-xl border border-champagne-500/20',
        'px-6 py-3 rounded-xl tracking-luxury',
        className
      )}
      style={{ x, y }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
    >
      {children}
    </motion.button>
  );
}

interface FloatingElementProps {
  children: React.ReactNode;
  className?: string;
  intensity?: 'subtle' | 'medium' | 'strong';
}

export function FloatingElement({ 
  children, 
  className, 
  intensity = 'medium' 
}: FloatingElementProps) {
  const intensityMap = {
    subtle: { y: [-2, 2], duration: 4 },
    medium: { y: [-5, 5], duration: 3 },
    strong: { y: [-10, 10], duration: 2.5 }
  };

  const config = intensityMap[intensity];

  return (
    <motion.div
      className={className}
      animate={{
        y: config.y,
        transition: {
          duration: config.duration,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }
      }}
    >
      {children}
    </motion.div>
  );
}