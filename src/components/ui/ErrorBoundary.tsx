import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshC<PERSON>, Home, Wifi, WifiOff } from 'lucide-react';
import { Button } from './Button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'component' | 'critical';
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
  isNetworkError?: boolean;
  retryCount: number;
}

export class ErrorBoundary extends Component<Props, State> {
  private errorId: string = '';

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, retryCount: 0 };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const isNetworkError = error.message.includes('fetch') || 
                          error.message.includes('network') || 
                          error.message.includes('Failed to fetch') ||
                          error.name === 'NetworkError';
    
    return { 
      hasError: true, 
      error, 
      errorId,
      isNetworkError
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Generate unique error ID for tracking
    this.errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.setState({ 
      error, 
      errorInfo, 
      errorId: this.errorId,
      isNetworkError: this.isNetworkError(error)
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Send to error reporting service in production
    this.reportError(error, errorInfo);
  }

  private isNetworkError(error: Error): boolean {
    return error.message.includes('fetch') || 
           error.message.includes('network') || 
           error.message.includes('Failed to fetch') ||
           error.name === 'NetworkError' ||
           error.message.includes('timeout');
  }

  private async reportError(error: Error, errorInfo: ErrorInfo) {
    if (process.env.NODE_ENV === 'production') {
      try {
        // In a real implementation, send to error reporting service
        // await errorReportingService.captureException(error, {
        //   extra: errorInfo,
        //   tags: {
        //     component: 'ErrorBoundary',
        //     level: this.props.level || 'component',
        //     errorId: this.errorId
        //   }
        // });
        
        // For now, send to backend error logging endpoint
        await fetch('/api/errors', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            errorId: this.errorId,
            message: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            level: this.props.level || 'component',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
          })
        }).catch(() => {
          // Silently fail if error reporting fails
          console.warn('Failed to report error to backend');
        });
      } catch (reportingError) {
        console.warn('Error reporting failed:', reportingError);
      }
    }
  }

  handleRetry = () => {
    const newRetryCount = this.state.retryCount + 1;
    
    // Limit retries to prevent infinite loops
    if (newRetryCount > 3) {
      console.warn('Maximum retry attempts reached');
      return;
    }

    this.setState({ 
      hasError: false, 
      error: undefined, 
      errorInfo: undefined,
      errorId: undefined,
      isNetworkError: false,
      retryCount: newRetryCount
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportIssue = () => {
    // Open support ticket or contact form
    const subject = encodeURIComponent(`Error Report - ${this.state.errorId}`);
    const body = encodeURIComponent(
      `Error ID: ${this.state.errorId}\n` +
      `Error: ${this.state.error?.message}\n` +
      `URL: ${window.location.href}\n` +
      `Time: ${new Date().toISOString()}\n\n` +
      `Please describe what you were doing when this error occurred:`
    );
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const isComponentLevel = this.props.level === 'component';
      const containerClass = isComponentLevel 
        ? "bg-platinum-900/95 rounded-2xl p-6 border border-red-500/20 shadow-xl backdrop-blur-sm"
        : "min-h-screen bg-luxury-gradient flex items-center justify-center px-6";

      const contentClass = isComponentLevel 
        ? "text-center"
        : "max-w-md w-full";

      return (
        <div className={containerClass}>
          <div className={contentClass}>
            {!isComponentLevel && (
              <div className="bg-platinum-900/95 rounded-2xl p-8 border border-champagne-500/20 shadow-2xl backdrop-blur-sm text-center">
                {this.renderErrorContent()}
              </div>
            )}
            {isComponentLevel && this.renderErrorContent()}
          </div>
        </div>
      );
    }

    return this.props.children;
  }

  private renderErrorContent() {
    const { isNetworkError, error, errorId, retryCount } = this.state;
    const maxRetriesReached = retryCount >= 3;

    return (
      <>
        <div className="w-16 h-16 bg-red-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
          {isNetworkError ? (
            <WifiOff className="w-8 h-8 text-red-400" />
          ) : (
            <AlertTriangle className="w-8 h-8 text-red-400" />
          )}
        </div>
        
        <h1 className="text-2xl font-serif font-bold text-platinum-50 mb-4">
          {isNetworkError ? 'Connection Issue' : 'Something went wrong'}
        </h1>
        
        <p className="text-platinum-300 mb-6">
          {isNetworkError 
            ? 'Unable to connect to our services. Please check your internet connection and try again.'
            : 'We apologize for the inconvenience. Our team has been notified and is working to resolve this issue.'
          }
        </p>

        {errorId && (
          <div className="bg-platinum-800/30 rounded-xl p-3 mb-6">
            <p className="text-xs text-platinum-400">
              Error ID: <span className="font-mono text-champagne-400">{errorId}</span>
            </p>
          </div>
        )}
        
        {process.env.NODE_ENV === 'development' && error && (
          <div className="bg-platinum-800/50 rounded-xl p-4 mb-6 text-left">
            <h3 className="text-sm font-semibold text-red-400 mb-2">Error Details:</h3>
            <pre className="text-xs text-platinum-400 overflow-auto max-h-32">
              {error.message}
            </pre>
            {error.stack && (
              <details className="mt-2">
                <summary className="text-xs text-platinum-500 cursor-pointer">Stack Trace</summary>
                <pre className="text-xs text-platinum-500 mt-1 overflow-auto max-h-24">
                  {error.stack}
                </pre>
              </details>
            )}
          </div>
        )}
        
        <div className="flex flex-col sm:flex-row gap-4">
          {!maxRetriesReached && (
            <Button
              onClick={this.handleRetry}
              variant="primary"
              className="flex-1"
            >
              {isNetworkError ? <Wifi className="w-4 h-4 mr-2" /> : <RefreshCw className="w-4 h-4 mr-2" />}
              {isNetworkError ? 'Reconnect' : 'Try Again'}
              {retryCount > 0 && ` (${retryCount}/3)`}
            </Button>
          )}
          
          <Button
            onClick={this.handleGoHome}
            variant="outline"
            className="flex-1"
          >
            <Home className="w-4 h-4 mr-2" />
            Go Home
          </Button>

          {process.env.NODE_ENV === 'production' && (
            <Button
              onClick={this.handleReportIssue}
              variant="ghost"
              size="sm"
              className="text-platinum-400 hover:text-platinum-200"
            >
              Report Issue
            </Button>
          )}
        </div>
      </>
    );
  }
}

// Hook version for functional components
export const useErrorHandler = () => {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Error caught by error handler:', error, errorInfo);
    
    // In a real app, you might want to send this to an error reporting service
    // like Sentry, LogRocket, etc.
    
    // For now, we'll just log it
    if (process.env.NODE_ENV === 'production') {
      // Send to error reporting service
      // errorReportingService.captureException(error, { extra: errorInfo });
    }
  };
};