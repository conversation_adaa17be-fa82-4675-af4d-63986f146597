import React, { useState } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { cn } from '../../lib/utils';
import { hierarchy } from '../../lib/typography';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  hierarchy?: 'primary' | 'secondary' | 'tertiary';
  tilt?: boolean;
  glow?: boolean;
  onClick?: () => void;
}

export function Card({
  children,
  className,
  hover = false,
  hierarchy: hierarchyLevel = 'primary',
  tilt = false,
  glow = false,
  onClick
}: CardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Tilt effect
  const x = useMotionValue(0);
  const y = useMotionValue(0);

  const mouseXSpring = useSpring(x);
  const mouseYSpring = useSpring(y);

  const rotateX = useTransform(mouseYSpring, [-0.5, 0.5], ["5deg", "-5deg"]);
  const rotateY = useTransform(mouseXSpring, [-0.5, 0.5], ["-5deg", "5deg"]);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!tilt) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const xPct = mouseX / width - 0.5;
    const yPct = mouseY / height - 0.5;

    x.set(xPct);
    y.set(yPct);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (tilt) {
      x.set(0);
      y.set(0);
    }
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const glowStyles = glow && isHovered ? {
    boxShadow: '0 25px 50px -12px rgba(212, 175, 55, 0.25), 0 0 0 1px rgba(212, 175, 55, 0.1)'
  } : {};

  return (
    <motion.div
      className={cn(
        'rounded-2xl backdrop-blur-sm transition-all duration-300',
        hierarchy.card[hierarchyLevel],
        hover && 'hover:shadow-champagne-500/10',
        onClick && 'cursor-pointer',
        className
      )}
      style={tilt ? {
        rotateX,
        rotateY,
        transformStyle: "preserve-3d",
        ...glowStyles
      } : glowStyles}
      whileHover={hover ? {
        y: -8,
        scale: 1.02,
        transition: {
          type: "spring",
          stiffness: 300,
          damping: 20
        }
      } : undefined}
      whileTap={onClick ? {
        scale: 0.98,
        transition: {
          type: "spring",
          stiffness: 600,
          damping: 30
        }
      } : undefined}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
      onClick={onClick}
    >
      <div style={tilt ? { transform: "translateZ(20px)" } : undefined}>
        {children}
      </div>
    </motion.div>
  );
}