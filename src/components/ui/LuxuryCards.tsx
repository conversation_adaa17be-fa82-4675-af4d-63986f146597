import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MapPin, Calendar, Users, Star, ArrowRight, Play, Heart, Share2 } from 'lucide-react';
import { cn, formatCurrency, formatDate } from '../../lib/utils';
import { Trip, Activity, Accommodation } from '../../types';
import { Button } from './Button';
import { InteractiveCard } from './InteractiveCard';

// Sophisticated Trip Card with enhanced image treatment and overlays
interface LuxuryTripCardProps {
  trip: Trip;
  variant?: 'standard' | 'featured' | 'compact';
  onSelect?: (trip: Trip) => void;
  onFavorite?: (tripId: string) => void;
  onShare?: (tripId: string) => void;
  className?: string;
}

export function LuxuryTripCard({ 
  trip, 
  variant = 'standard',
  onSelect,
  onFavorite,
  onShare,
  className 
}: LuxuryTripCardProps) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handleImageLoad = () => setIsImageLoaded(true);

  const cardVariants = {
    standard: 'aspect-[4/5]',
    featured: 'aspect-[16/10]',
    compact: 'aspect-[3/4]'
  };

  return (
    <InteractiveCard 
      hover 
      tilt={variant === 'featured'} 
      glow={variant === 'featured'}
      className={cn('group overflow-hidden', cardVariants[variant], className)}
      onClick={() => onSelect?.(trip)}
    >
      {/* Image Section with Sophisticated Overlay */}
      <div className="relative h-full overflow-hidden">
        {/* Main Image */}
        <motion.img
          src={trip.images[currentImageIndex] || trip.heroImage}
          alt={trip.title}
          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
          onLoad={handleImageLoad}
          initial={{ opacity: 0 }}
          animate={{ opacity: isImageLoaded ? 1 : 0 }}
          transition={{ duration: 0.6 }}
        />

        {/* Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/40 via-transparent to-transparent" />

        {/* Status Badge */}
        <div className="absolute top-4 right-4 z-10">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className={cn(
              'px-3 py-1.5 rounded-full text-xs font-semibold uppercase tracking-wider backdrop-blur-md border',
              trip.status === 'confirmed' 
                ? 'bg-emerald-500/20 text-emerald-300 border-emerald-500/30'
                : trip.status === 'pending'
                ? 'bg-amber-500/20 text-amber-300 border-amber-500/30'
                : 'bg-champagne-500/20 text-champagne-300 border-champagne-500/30'
            )}
          >
            {trip.status}
          </motion.div>
        </div>

        {/* Action Buttons */}
        <div className="absolute top-4 left-4 z-10 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          {onFavorite && (
            <Button
              variant="ghost"
              size="sm"
              className="w-10 h-10 p-0 bg-black/20 backdrop-blur-md border border-white/20 hover:bg-white/20"
              onClick={(e) => {
                e.stopPropagation();
                onFavorite(trip.id);
              }}
            >
              <Heart className="w-4 h-4 text-white" />
            </Button>
          )}
          {onShare && (
            <Button
              variant="ghost"
              size="sm"
              className="w-10 h-10 p-0 bg-black/20 backdrop-blur-md border border-white/20 hover:bg-white/20"
              onClick={(e) => {
                e.stopPropagation();
                onShare(trip.id);
              }}
            >
              <Share2 className="w-4 h-4 text-white" />
            </Button>
          )}
        </div>

        {/* Image Navigation Dots */}
        {trip.images.length > 1 && (
          <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            {trip.images.map((_, index) => (
              <button
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentImageIndex(index);
                }}
                className={cn(
                  'w-2 h-2 rounded-full transition-all duration-300',
                  index === currentImageIndex 
                    ? 'bg-champagne-400 scale-125' 
                    : 'bg-white/50 hover:bg-white/80'
                )}
              />
            ))}
          </div>
        )}

        {/* Content Overlay */}
        <div className="absolute inset-0 flex flex-col justify-end p-6">
          {/* Trip Title and Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-4"
          >
            <h3 className="text-xl md:text-2xl font-serif font-bold text-white mb-2 line-clamp-2">
              {trip.title}
            </h3>
            <p className="text-white/80 text-sm line-clamp-2 mb-3">
              {trip.description}
            </p>
          </motion.div>

          {/* Trip Metadata */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-2 mb-4"
          >
            <div className="flex items-center text-white/90 text-sm">
              <MapPin className="w-4 h-4 mr-2 text-champagne-400" />
              <span className="font-medium">{trip.destinations?.join(', ') || trip.destination}</span>
            </div>
            
            <div className="flex items-center justify-between text-white/90 text-sm">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-2 text-champagne-400" />
                <span>{formatDate(trip.startDate)}</span>
              </div>
              
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-2 text-champagne-400" />
                <span>{trip.guestCount} guests</span>
              </div>
            </div>
          </motion.div>

          {/* Price and CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="flex items-center justify-between"
          >
            <div>
              <p className="text-white/70 text-xs uppercase tracking-wider font-medium">Total Investment</p>
              <p className="text-champagne-400 font-serif font-bold text-lg md:text-xl">
                {formatCurrency(trip.totalPrice || trip.totalCost)}
              </p>
            </div>
            
            <Button
              variant="secondary"
              size="sm"
              className="bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-4 group-hover:translate-x-0"
            >
              Explore
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </motion.div>
        </div>
      </div>
    </InteractiveCard>
  );
}

// Gallery-style Accommodation Card with smooth transitions
interface AccommodationGalleryCardProps {
  accommodation: Accommodation;
  onSelect?: (accommodation: Accommodation) => void;
  className?: string;
}

export function AccommodationGalleryCard({ 
  accommodation, 
  onSelect,
  className 
}: AccommodationGalleryCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  return (
    <InteractiveCard 
      hover 
      className={cn('group overflow-hidden aspect-[4/3]', className)}
      onClick={() => onSelect?.(accommodation)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative h-full">
        {/* Image Gallery */}
        <div className="relative h-2/3 overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.img
              key={currentImageIndex}
              src={accommodation.images[currentImageIndex]}
              alt={accommodation.name}
              className="w-full h-full object-cover"
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.6 }}
            />
          </AnimatePresence>

          {/* Image Navigation */}
          {accommodation.images.length > 1 && isHovered && (
            <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1">
              {accommodation.images.map((_, index) => (
                <button
                  key={index}
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentImageIndex(index);
                  }}
                  className={cn(
                    'w-1.5 h-1.5 rounded-full transition-all duration-300',
                    index === currentImageIndex 
                      ? 'bg-champagne-400 scale-125' 
                      : 'bg-white/60 hover:bg-white/90'
                  )}
                />
              ))}
            </div>
          )}

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
        </div>

        {/* Content Section */}
        <div className="h-1/3 p-4 bg-platinum-900/95 backdrop-blur-sm">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1">
              <h4 className="font-serif font-bold text-platinum-50 text-lg line-clamp-1">
                {accommodation.name}
              </h4>
              <p className="text-champagne-400 text-sm font-medium">
                {accommodation.partnerName}
              </p>
            </div>
            <div className="text-right">
              <p className="text-platinum-50 font-semibold">
                {formatCurrency(accommodation.cost)}
              </p>
              <p className="text-platinum-400 text-xs">per night</p>
            </div>
          </div>

          {/* Amenities Preview */}
          <div className="flex flex-wrap gap-1 mb-2">
            {accommodation.amenities.slice(0, 3).map((amenity, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-champagne-500/20 text-champagne-400 rounded text-xs font-medium"
              >
                {amenity}
              </span>
            ))}
            {accommodation.amenities.length > 3 && (
              <span className="px-2 py-1 bg-platinum-700/50 text-platinum-300 rounded text-xs">
                +{accommodation.amenities.length - 3} more
              </span>
            )}
          </div>

          {/* Rating */}
          <div className="flex items-center">
            <div className="flex items-center mr-2">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    'w-3 h-3',
                    i < Math.floor(accommodation.rating || 5)
                      ? 'text-champagne-400 fill-current'
                      : 'text-platinum-600'
                  )}
                />
              ))}
            </div>
            <span className="text-platinum-300 text-xs">
              {accommodation.rating || '5.0'} • {accommodation.type}
            </span>
          </div>
        </div>
      </div>
    </InteractiveCard>
  );
}

// Magazine-style Activity Card with asymmetric compositions
interface MagazineActivityCardProps {
  activity: Activity;
  layout?: 'left' | 'right' | 'full';
  onSelect?: (activity: Activity) => void;
  onAskQuestion?: (activityId: string, context: string) => void;
  className?: string;
}

export function MagazineActivityCard({
  activity,
  layout = 'left',
  onSelect,
  onAskQuestion,
  className
}: MagazineActivityCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const layoutClasses = {
    left: 'md:flex-row',
    right: 'md:flex-row-reverse',
    full: 'flex-col'
  };

  const imageClasses = {
    left: 'md:w-2/5',
    right: 'md:w-2/5',
    full: 'w-full h-64'
  };

  const contentClasses = {
    left: 'md:w-3/5',
    right: 'md:w-3/5',
    full: 'w-full'
  };

  return (
    <InteractiveCard
      hover
      className={cn('group overflow-hidden', className)}
      onClick={() => onSelect?.(activity)}
    >
      <div className={cn('flex flex-col', layoutClasses[layout])}>
        {/* Image Section */}
        <div className={cn('relative overflow-hidden', imageClasses[layout])}>
          <motion.img
            src={activity.images[0]}
            alt={activity.title}
            className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
            whileHover={{ scale: 1.05 }}
          />

          {/* Time Badge */}
          <div className="absolute top-4 left-4">
            <div className="bg-black/60 backdrop-blur-md text-white px-3 py-1.5 rounded-full text-sm font-medium">
              {activity.startTime} - {activity.endTime}
            </div>
          </div>

          {/* Price Badge */}
          <div className="absolute top-4 right-4">
            <div className="bg-champagne-500/90 backdrop-blur-md text-platinum-900 px-3 py-1.5 rounded-full text-sm font-bold">
              {formatCurrency(activity.cost)}
            </div>
          </div>

          {/* Gradient Overlay for text readability */}
          {layout === 'full' && (
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          )}
        </div>

        {/* Content Section */}
        <div className={cn('p-6 bg-platinum-900/95 backdrop-blur-sm', contentClasses[layout])}>
          {/* Header */}
          <div className="mb-4">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h3 className="text-xl font-serif font-bold text-platinum-50 mb-1 line-clamp-2">
                  {activity.title}
                </h3>
                <p className="text-champagne-400 font-medium text-sm">
                  {activity.partnerName}
                </p>
              </div>

              {onAskQuestion && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAskQuestion(activity.id, `${activity.title} at ${activity.location}`);
                  }}
                >
                  <Play className="w-4 h-4" />
                </Button>
              )}
            </div>

            {/* Location */}
            <div className="flex items-center text-platinum-300 text-sm mb-3">
              <MapPin className="w-4 h-4 mr-2 text-champagne-400" />
              <span>{activity.location}</span>
            </div>
          </div>

          {/* Description */}
          <div className="mb-4">
            <motion.p
              className={cn(
                'text-platinum-300 text-sm leading-relaxed',
                !isExpanded && 'line-clamp-3'
              )}
              animate={{ height: isExpanded ? 'auto' : undefined }}
            >
              {activity.description}
            </motion.p>

            {activity.description.length > 150 && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsExpanded(!isExpanded);
                }}
                className="text-champagne-400 text-sm font-medium mt-2 hover:text-champagne-300 transition-colors"
              >
                {isExpanded ? 'Show less' : 'Read more'}
              </button>
            )}
          </div>

          {/* Activity Tags/Features */}
          {activity.features && (
            <div className="flex flex-wrap gap-2 mb-4">
              {activity.features.slice(0, 4).map((feature, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-champagne-500/20 text-champagne-400 rounded-full text-xs font-medium"
                >
                  {feature}
                </span>
              ))}
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-platinum-700/30">
            <div className="flex items-center text-platinum-400 text-sm">
              <Star className="w-4 h-4 mr-1 text-champagne-400 fill-current" />
              <span className="font-medium">Exclusive Experience</span>
            </div>

            <div className="text-platinum-400 text-sm">
              Duration: {activity.duration || '2-3 hours'}
            </div>
          </div>
        </div>
      </div>
    </InteractiveCard>
  );
}

// Responsive Masonry Layout Component
interface MasonryLayoutProps {
  children: React.ReactNode;
  columns?: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function MasonryLayout({
  children,
  columns = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = 'lg',
  className
}: MasonryLayoutProps) {
  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8',
    xl: 'gap-12'
  };

  const columnClasses = `columns-${columns.sm} md:columns-${columns.md} lg:columns-${columns.lg} xl:columns-${columns.xl}`;

  return (
    <div className={cn(
      'w-full',
      columnClasses,
      gapClasses[gap],
      className
    )}>
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          className="break-inside-avoid mb-6 md:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.6 }}
        >
          {child}
        </motion.div>
      ))}
    </div>
  );
}

// Adaptive Grid Layout that responds to content
interface AdaptiveGridProps {
  children: React.ReactNode;
  minItemWidth?: number;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function AdaptiveGrid({
  children,
  minItemWidth = 320,
  gap = 'lg',
  className
}: AdaptiveGridProps) {
  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8',
    xl: 'gap-12'
  };

  return (
    <div
      className={cn(
        'grid w-full',
        gapClasses[gap],
        className
      )}
      style={{
        gridTemplateColumns: `repeat(auto-fit, minmax(${minItemWidth}px, 1fr))`
      }}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.1, duration: 0.6 }}
        >
          {child}
        </motion.div>
      ))}
    </div>
  );
}
