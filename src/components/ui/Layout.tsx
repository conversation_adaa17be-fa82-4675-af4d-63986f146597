import React from 'react';
import { cn } from '../../lib/utils';
import { spacing, hierarchy } from '../../lib/typography';

// Section Container with luxury spacing
interface SectionProps {
  children: React.ReactNode;
  spacing?: 'tight' | 'normal' | 'relaxed' | 'loose' | 'luxury';
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

export function Section({ 
  children, 
  spacing: spacingSize = 'normal', 
  className,
  as: Component = 'section'
}: SectionProps) {
  return (
    <Component className={cn(spacing.section[spacingSize], className)}>
      {children}
    </Component>
  );
}

// Content Container with proper density
interface ContentContainerProps {
  children: React.ReactNode;
  spacing?: 'tight' | 'normal' | 'relaxed' | 'loose';
  padding?: 'tight' | 'normal' | 'relaxed' | 'luxury';
  className?: string;
}

export function ContentContainer({ 
  children, 
  spacing: spacingSize = 'normal',
  padding = 'normal',
  className 
}: ContentContainerProps) {
  return (
    <div className={cn(
      spacing.content[spacingSize],
      spacing.container[padding],
      className
    )}>
      {children}
    </div>
  );
}

// Visual Separator Component
interface SeparatorProps {
  variant?: 'luxury' | 'premium' | 'standard' | 'subtle';
  orientation?: 'horizontal' | 'vertical';
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function Separator({ 
  variant = 'standard',
  orientation = 'horizontal',
  spacing: spacingSize = 'md',
  className 
}: SeparatorProps) {
  const variants = {
    luxury: 'border-champagne-500/30',
    premium: 'border-rose-400/30',
    standard: 'border-platinum-600/30',
    subtle: 'border-platinum-700/20',
  };

  const spacingClasses = {
    sm: orientation === 'horizontal' ? 'my-4' : 'mx-4',
    md: orientation === 'horizontal' ? 'my-6' : 'mx-6',
    lg: orientation === 'horizontal' ? 'my-8' : 'mx-8',
    xl: orientation === 'horizontal' ? 'my-12' : 'mx-12',
  };

  const orientationClasses = orientation === 'horizontal' 
    ? 'border-t w-full' 
    : 'border-l h-full';

  return (
    <div className={cn(
      orientationClasses,
      variants[variant],
      spacingClasses[spacingSize],
      className
    )} />
  );
}

// Grid Layout with luxury spacing
interface LuxuryGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4 | 6;
  gap?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  className?: string;
}

export function LuxuryGrid({ 
  children, 
  columns = 3, 
  gap = 'lg',
  className 
}: LuxuryGridProps) {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    6: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-6',
  };

  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8',
    xl: 'gap-12',
    '2xl': 'gap-16',
  };

  return (
    <div className={cn(
      'grid',
      columnClasses[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
}

// Flex Layout with luxury spacing
interface LuxuryFlexProps {
  children: React.ReactNode;
  direction?: 'row' | 'col';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  wrap?: boolean;
  className?: string;
}

export function LuxuryFlex({ 
  children, 
  direction = 'row',
  align = 'start',
  justify = 'start',
  gap = 'md',
  wrap = false,
  className 
}: LuxuryFlexProps) {
  const directionClasses = {
    row: 'flex-row',
    col: 'flex-col',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
    '2xl': 'gap-12',
  };

  return (
    <div className={cn(
      'flex',
      directionClasses[direction],
      alignClasses[align],
      justifyClasses[justify],
      gapClasses[gap],
      wrap && 'flex-wrap',
      className
    )}>
      {children}
    </div>
  );
}

// Enhanced Card with hierarchy
interface HierarchyCardProps {
  children: React.ReactNode;
  hierarchy?: 'primary' | 'secondary' | 'tertiary';
  padding?: 'tight' | 'normal' | 'relaxed' | 'luxury';
  hover?: boolean;
  className?: string;
}

export function HierarchyCard({ 
  children, 
  hierarchy: hierarchyLevel = 'primary',
  padding = 'normal',
  hover = false,
  className 
}: HierarchyCardProps) {
  return (
    <div className={cn(
      'rounded-2xl backdrop-blur-sm transition-all duration-300',
      hierarchy.card[hierarchyLevel],
      spacing.container[padding],
      hover && 'hover:shadow-champagne-500/10 hover:-translate-y-1',
      className
    )}>
      {children}
    </div>
  );
}

// Information Hierarchy Component
interface InfoHierarchyProps {
  primary: React.ReactNode;
  secondary?: React.ReactNode;
  tertiary?: React.ReactNode;
  metadata?: React.ReactNode;
  layout?: 'vertical' | 'horizontal';
  className?: string;
}

export function InfoHierarchy({ 
  primary, 
  secondary, 
  tertiary, 
  metadata,
  layout = 'vertical',
  className 
}: InfoHierarchyProps) {
  if (layout === 'horizontal') {
    return (
      <div className={cn('flex items-start justify-between', className)}>
        <div className="space-y-2 flex-1">
          <div className="text-platinum-50 font-semibold">{primary}</div>
          {secondary && <div className="text-platinum-300">{secondary}</div>}
          {tertiary && <div className="text-platinum-400 text-sm">{tertiary}</div>}
        </div>
        {metadata && (
          <div className="text-right space-y-1 ml-6">
            {metadata}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-3', className)}>
      <div className="text-platinum-50 font-semibold text-lg">{primary}</div>
      {secondary && <div className="text-platinum-300">{secondary}</div>}
      {tertiary && <div className="text-platinum-400 text-sm">{tertiary}</div>}
      {metadata && (
        <div className="pt-2 border-t border-platinum-700/30">
          {metadata}
        </div>
      )}
    </div>
  );
}

// Whitespace Component for strategic spacing
interface WhitespaceProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  className?: string;
}

export function Whitespace({ size = 'md', className }: WhitespaceProps) {
  const sizeClasses = {
    xs: 'h-2',
    sm: 'h-4',
    md: 'h-6',
    lg: 'h-8',
    xl: 'h-12',
    '2xl': 'h-16',
    '3xl': 'h-24',
  };

  return <div className={cn(sizeClasses[size], className)} />;
}