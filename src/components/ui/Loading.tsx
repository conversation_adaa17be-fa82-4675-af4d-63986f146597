import React from 'react';
import { motion } from 'framer-motion';
import { Crown, Loader2 } from 'lucide-react';
import { cn } from '../../lib/utils';
import { loadingAnimations } from '../../lib/animations';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'luxury' | 'minimal' | 'elegant';
  className?: string;
}

export function LoadingSpinner({ 
  size = 'md', 
  variant = 'luxury',
  className 
}: LoadingSpinnerProps) {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  if (variant === 'luxury') {
    return (
      <motion.div
        variants={loadingAnimations.luxuryPulse}
        animate="animate"
        className={cn('flex items-center justify-center', className)}
      >
        <div className="relative">
          <motion.div
            variants={loadingAnimations.spinner}
            animate="animate"
            className={cn(
              'border-2 border-champagne-500/30 border-t-champagne-400 rounded-full',
              sizes[size]
            )}
          />
          <Crown className={cn(
            'absolute inset-0 m-auto text-champagne-400',
            size === 'sm' ? 'w-2 h-2' :
            size === 'md' ? 'w-3 h-3' :
            size === 'lg' ? 'w-4 h-4' : 'w-6 h-6'
          )} />
        </div>
      </motion.div>
    );
  }

  if (variant === 'elegant') {
    return (
      <motion.div
        variants={loadingAnimations.spinner}
        animate="animate"
        className={cn(className)}
      >
        <div className={cn(
          'border-2 border-platinum-600/30 border-t-champagne-400 rounded-full',
          sizes[size]
        )} />
      </motion.div>
    );
  }

  return (
    <motion.div
      variants={loadingAnimations.spinner}
      animate="animate"
      className={cn(className)}
    >
      <Loader2 className={cn('text-champagne-400', sizes[size])} />
    </motion.div>
  );
}

interface LoadingDotsProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingDots({ size = 'md', className }: LoadingDotsProps) {
  const dotSizes = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  };

  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          variants={loadingAnimations.dots}
          animate="animate"
          transition={{ delay: index * 0.2 }}
          className={cn(
            'bg-champagne-400 rounded-full',
            dotSizes[size]
          )}
        />
      ))}
    </div>
  );
}

interface LoadingSkeletonProps {
  className?: string;
  lines?: number;
  variant?: 'card' | 'text' | 'image';
}

export function LoadingSkeleton({ 
  className, 
  lines = 3, 
  variant = 'text' 
}: LoadingSkeletonProps) {
  const shimmerGradient = "linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent)";

  if (variant === 'card') {
    return (
      <div className={cn('animate-pulse space-y-4', className)}>
        <motion.div
          className="h-48 bg-platinum-800/50 rounded-xl"
          style={{
            background: `linear-gradient(90deg, #262626, #404040, #262626)`,
            backgroundSize: '200% 100%'
          }}
          variants={{
            animate: {
              backgroundPosition: ['200% 0', '-200% 0'],
              transition: {
                duration: 2,
                repeat: Infinity,
                ease: "linear"
              }
            }
          }}
          animate="animate"
        />
        <div className="space-y-2">
          <motion.div
            className="h-4 bg-platinum-800/50 rounded w-3/4"
            style={{
              background: `linear-gradient(90deg, #262626, #404040, #262626)`,
              backgroundSize: '200% 100%'
            }}
            variants={{
              animate: {
                backgroundPosition: ['200% 0', '-200% 0'],
                transition: {
                  duration: 2,
                  repeat: Infinity,
                  ease: "linear",
                  delay: 0.1
                }
              }
            }}
            animate="animate"
          />
          <motion.div
            className="h-4 bg-platinum-800/50 rounded w-1/2"
            style={{
              background: `linear-gradient(90deg, #262626, #404040, #262626)`,
              backgroundSize: '200% 100%'
            }}
            variants={{
              animate: {
                backgroundPosition: ['200% 0', '-200% 0'],
                transition: {
                  duration: 2,
                  repeat: Infinity,
                  ease: "linear",
                  delay: 0.2
                }
              }
            }}
            animate="animate"
          />
        </div>
      </div>
    );
  }

  if (variant === 'image') {
    return (
      <motion.div
        className={cn('bg-platinum-800/50 rounded-xl', className)}
        style={{
          background: `linear-gradient(90deg, #262626, #404040, #262626)`,
          backgroundSize: '200% 100%'
        }}
        variants={{
          animate: {
            backgroundPosition: ['200% 0', '-200% 0'],
            transition: {
              duration: 2,
              repeat: Infinity,
              ease: "linear"
            }
          }
        }}
        animate="animate"
      />
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <motion.div
          key={index}
          className={cn(
            'h-4 bg-platinum-800/50 rounded',
            index === lines - 1 ? 'w-2/3' : 'w-full'
          )}
          style={{
            background: `linear-gradient(90deg, #262626, #404040, #262626)`,
            backgroundSize: '200% 100%'
          }}
          variants={{
            animate: {
              backgroundPosition: ['200% 0', '-200% 0'],
              transition: {
                duration: 2,
                repeat: Infinity,
                ease: "linear",
                delay: index * 0.1
              }
            }
          }}
          animate="animate"
        />
      ))}
    </div>
  );
}

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  variant?: 'luxury' | 'minimal';
}

export function LoadingOverlay({ 
  isVisible, 
  message = "Loading...", 
  variant = 'luxury' 
}: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-platinum-950/80 backdrop-blur-sm z-50 flex items-center justify-center"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-platinum-900/95 border border-champagne-500/20 rounded-2xl p-8 text-center backdrop-blur-sm"
      >
        <LoadingSpinner size="lg" variant={variant} className="mb-4" />
        <p className="text-platinum-300 text-body-lg font-medium">{message}</p>
      </motion.div>
    </motion.div>
  );
}