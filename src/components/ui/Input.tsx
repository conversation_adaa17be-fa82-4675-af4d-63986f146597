import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  success?: boolean;
  icon?: React.ReactNode;
}

export function Input({ 
  label, 
  error, 
  success = false,
  icon,
  className, 
  onFocus,
  onBlur,
  ...props 
}: InputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(false);

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    setHasValue(e.target.value.length > 0);
    onBlur?.(e);
  };

  return (
    <div className="space-y-2">
      {label && (
        <motion.label
          className={cn(
            'block text-sm font-medium transition-colors duration-200',
            isFocused || hasValue 
              ? 'text-champagne-400' 
              : 'text-platinum-200'
          )}
          animate={{
            scale: isFocused ? 1.02 : 1,
            transition: { duration: 0.2 }
          }}
        >
          {label}
        </motion.label>
      )}
      
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-platinum-400">
            {icon}
          </div>
        )}
        
        <motion.input
          className={cn(
            'w-full px-4 py-3 bg-platinum-800/50 border rounded-xl text-platinum-50 placeholder-platinum-400 focus:outline-none transition-all duration-200',
            icon && 'pl-10',
            error 
              ? 'border-rose-500 focus:ring-2 focus:ring-rose-400/50 focus:border-rose-400' 
              : success
              ? 'border-green-500 focus:ring-2 focus:ring-green-400/50 focus:border-green-400'
              : 'border-champagne-500/30 focus:ring-2 focus:ring-champagne-400/50 focus:border-champagne-400',
            className
          )}
          animate={{
            scale: isFocused ? 1.01 : 1,
            boxShadow: isFocused 
              ? error 
                ? '0 0 0 3px rgba(244, 63, 94, 0.1)' 
                : success
                ? '0 0 0 3px rgba(34, 197, 94, 0.1)'
                : '0 0 0 3px rgba(212, 175, 55, 0.1)'
              : '0 0 0 0px transparent',
            transition: { duration: 0.2 }
          }}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...props}
        />
        
        {/* Focus indicator line */}
        <motion.div
          className={cn(
            'absolute bottom-0 left-0 h-0.5 rounded-full',
            error 
              ? 'bg-rose-400' 
              : success
              ? 'bg-green-400'
              : 'bg-champagne-400'
          )}
          initial={{ width: 0 }}
          animate={{ 
            width: isFocused ? '100%' : '0%',
            transition: { duration: 0.3, ease: "easeOut" }
          }}
        />
      </div>
      
      <AnimatePresence>
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="text-sm text-rose-400"
          >
            {error}
          </motion.p>
        )}
      </AnimatePresence>
    </div>
  );
}