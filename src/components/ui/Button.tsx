import React, { useState } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { cn } from '../../lib/utils';
import { LoadingSpinner } from './Loading';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'luxury';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  loading?: boolean;
  magnetic?: boolean;
  glow?: boolean;
}

export function Button({
  variant = 'primary',
  size = 'md',
  className,
  children,
  loading = false,
  magnetic = false,
  glow = false,
  disabled,
  ...props
}: ButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  
  // Magnetic effect
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const mouseXSpring = useSpring(x);
  const mouseYSpring = useSpring(y);

  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed tracking-luxury relative overflow-hidden';
  
  const variants = {
    primary: 'bg-champagne-gradient hover:from-champagne-600 hover:to-champagne-400 text-platinum-900 shadow-lg hover:shadow-xl border border-champagne-500/20 font-semibold',
    secondary: 'bg-platinum-800 hover:bg-platinum-700 text-platinum-50 shadow-lg hover:shadow-xl border border-platinum-600/30 font-medium',
    outline: 'border-2 border-champagne-500 text-champagne-500 hover:bg-champagne-500 hover:text-platinum-900 font-medium',
    ghost: 'text-platinum-400 hover:text-platinum-50 hover:bg-platinum-800/50 font-normal',
    luxury: 'bg-gradient-to-r from-champagne-500 via-champagne-400 to-champagne-500 bg-size-200 hover:bg-pos-100 text-platinum-900 shadow-lg hover:shadow-2xl border border-champagne-500/30 font-semibold'
  };

  const sizes = {
    sm: 'px-4 py-2 text-body-md rounded-lg',
    md: 'px-6 py-3 text-body-lg rounded-xl',
    lg: 'px-8 py-4 text-heading-sm rounded-xl'
  };

  const glowStyles = glow ? {
    boxShadow: isHovered 
      ? '0 0 20px rgba(212, 175, 55, 0.4), 0 0 40px rgba(212, 175, 55, 0.2)' 
      : '0 0 10px rgba(212, 175, 55, 0.2)'
  } : {};

  const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!magnetic) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) * 0.15;
    const deltaY = (e.clientY - centerY) * 0.15;
    
    x.set(deltaX);
    y.set(deltaY);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (magnetic) {
      x.set(0);
      y.set(0);
    }
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  return (
    <motion.button
      className={cn(baseClasses, variants[variant], sizes[size], className)}
      style={magnetic ? { x: mouseXSpring, y: mouseYSpring, ...glowStyles } : glowStyles}
      whileHover={{ 
        scale: 1.02,
        transition: {
          type: "spring",
          stiffness: 400,
          damping: 25
        }
      }}
      whileTap={{ 
        scale: 0.98,
        transition: {
          type: "spring",
          stiffness: 600,
          damping: 30
        }
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
      disabled={disabled || loading}
      {...props}
    >
      {/* Shimmer effect for luxury variant */}
      {variant === 'luxury' && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
          initial={{ x: '-100%' }}
          animate={isHovered ? { x: '100%' } : { x: '-100%' }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
        />
      )}
      
      {/* Loading state */}
      {loading ? (
        <div className="flex items-center space-x-2">
          <LoadingSpinner size="sm" variant="minimal" />
          <span>Loading...</span>
        </div>
      ) : (
        children
      )}
    </motion.button>
  );
}