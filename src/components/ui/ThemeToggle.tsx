import React from 'react';
import { motion } from 'framer-motion';
import { Sun, Moon } from 'lucide-react';
import { useThemeState } from '../../hooks/useTheme';
import { Button } from './Button';

export function ThemeToggle() {
  const { theme, toggleTheme } = useThemeState();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      className="relative w-10 h-10 p-0 rounded-full"
      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
    >
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        initial={false}
        animate={{
          scale: theme === 'dark' ? 1 : 0,
          opacity: theme === 'dark' ? 1 : 0,
        }}
        transition={{ duration: 0.2 }}
      >
        <Moon className="w-5 h-5" />
      </motion.div>
      
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        initial={false}
        animate={{
          scale: theme === 'light' ? 1 : 0,
          opacity: theme === 'light' ? 1 : 0,
        }}
        transition={{ duration: 0.2 }}
      >
        <Sun className="w-5 h-5" />
      </motion.div>
    </Button>
  );
}

// Advanced Theme Toggle with luxury styling
export function LuxuryThemeToggle() {
  const { theme, toggleTheme } = useThemeState();

  return (
    <motion.button
      onClick={toggleTheme}
      className="relative w-14 h-8 bg-platinum-800 dark:bg-platinum-700 rounded-full p-1 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-champagne-400 focus:ring-offset-2 focus:ring-offset-platinum-900"
      whileTap={{ scale: 0.95 }}
    >
      {/* Track */}
      <div className="w-full h-full bg-gradient-to-r from-platinum-600 to-platinum-500 dark:from-platinum-800 dark:to-platinum-700 rounded-full" />
      
      {/* Thumb */}
      <motion.div
        className="absolute top-1 w-6 h-6 bg-champagne-gradient rounded-full shadow-lg flex items-center justify-center"
        animate={{
          x: theme === 'dark' ? 0 : 24,
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 30
        }}
      >
        <motion.div
          animate={{
            rotate: theme === 'dark' ? 0 : 180,
          }}
          transition={{ duration: 0.3 }}
        >
          {theme === 'dark' ? (
            <Moon className="w-3 h-3 text-platinum-900" />
          ) : (
            <Sun className="w-3 h-3 text-platinum-900" />
          )}
        </motion.div>
      </motion.div>
    </motion.button>
  );
}
