import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, X, AlertCircle, Info, Crown } from 'lucide-react';
import { cn } from '../../lib/utils';

interface ToastProps {
  type: 'success' | 'error' | 'warning' | 'info' | 'luxury';
  title: string;
  message?: string;
  isVisible: boolean;
  onClose: () => void;
  duration?: number;
}

export function Toast({ 
  type, 
  title, 
  message, 
  isVisible, 
  onClose, 
  duration = 5000 
}: ToastProps) {
  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(onClose, duration);
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  const icons = {
    success: Check,
    error: X,
    warning: AlertCircle,
    info: Info,
    luxury: Crown
  };

  const styles = {
    success: 'bg-green-500/20 border-green-500/30 text-green-400',
    error: 'bg-rose-500/20 border-rose-500/30 text-rose-400',
    warning: 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400',
    info: 'bg-blue-500/20 border-blue-500/30 text-blue-400',
    luxury: 'bg-champagne-500/20 border-champagne-500/30 text-champagne-400'
  };

  const Icon = icons[type];

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50, scale: 0.9 }}
          animate={{ 
            opacity: 1, 
            y: 0, 
            scale: 1,
            transition: {
              type: "spring",
              stiffness: 500,
              damping: 25
            }
          }}
          exit={{ 
            opacity: 0, 
            y: -20, 
            scale: 0.95,
            transition: { duration: 0.2 }
          }}
          className={cn(
            'relative z-50 p-3 md:p-4 rounded-xl border backdrop-blur-sm',
            'w-full max-w-sm shadow-lg',
            styles[type]
          )}
        >
          <div className="flex items-start space-x-2 md:space-x-3">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ 
                scale: 1,
                transition: {
                  type: "spring",
                  stiffness: 600,
                  damping: 25,
                  delay: 0.1
                }
              }}
              className="flex-shrink-0"
            >
              <Icon className="w-4 h-4 md:w-5 md:h-5 mt-0.5" />
            </motion.div>
            <div className="flex-1 min-w-0">
              <h4 className="font-semibold text-platinum-50 text-sm md:text-base truncate">{title}</h4>
              {message && (
                <p className="text-xs md:text-sm text-platinum-300 mt-1 line-clamp-2">{message}</p>
              )}
            </div>
            <button
              onClick={onClose}
              className="text-platinum-400 hover:text-platinum-200 transition-colors flex-shrink-0 p-1 -m-1"
            >
              <X className="w-3 h-3 md:w-4 md:h-4" />
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

interface ConfirmationAnimationProps {
  isVisible: boolean;
  onComplete?: () => void;
  variant?: 'success' | 'luxury';
}

export function ConfirmationAnimation({ 
  isVisible, 
  onComplete,
  variant = 'success' 
}: ConfirmationAnimationProps) {
  useEffect(() => {
    if (isVisible && onComplete) {
      const timer = setTimeout(onComplete, 2000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, onComplete]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-platinum-950/80 backdrop-blur-sm z-50 flex items-center justify-center"
        >
          <motion.div
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ 
              scale: 1, 
              opacity: 1,
              transition: {
                type: "spring",
                stiffness: 300,
                damping: 20
              }
            }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="text-center"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ 
                scale: 1,
                transition: {
                  type: "spring",
                  stiffness: 400,
                  damping: 15,
                  delay: 0.2
                }
              }}
              className={cn(
                'w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4',
                variant === 'luxury' 
                  ? 'bg-champagne-gradient' 
                  : 'bg-green-500'
              )}
            >
              {variant === 'luxury' ? (
                <Crown className="w-10 h-10 text-platinum-900" />
              ) : (
                <Check className="w-10 h-10 text-white" />
              )}
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ 
                opacity: 1, 
                y: 0,
                transition: { delay: 0.4 }
              }}
            >
              <h3 className="text-2xl font-serif font-bold text-platinum-50 mb-2">
                {variant === 'luxury' ? 'Exquisite!' : 'Success!'}
              </h3>
              <p className="text-platinum-300">
                {variant === 'luxury' 
                  ? 'Your luxury experience has been curated'
                  : 'Action completed successfully'
                }
              </p>
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

interface PulseIndicatorProps {
  isActive: boolean;
  className?: string;
  color?: 'champagne' | 'green' | 'blue' | 'rose';
}

export function PulseIndicator({ 
  isActive, 
  className, 
  color = 'champagne' 
}: PulseIndicatorProps) {
  const colors = {
    champagne: 'bg-champagne-400',
    green: 'bg-green-400',
    blue: 'bg-blue-400',
    rose: 'bg-rose-400'
  };

  return (
    <div className={cn('relative', className)}>
      <div className={cn('w-3 h-3 rounded-full', colors[color])} />
      {isActive && (
        <motion.div
          className={cn('absolute inset-0 rounded-full', colors[color])}
          animate={{
            scale: [1, 2, 1],
            opacity: [1, 0, 1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}
    </div>
  );
}

interface ProgressRingProps {
  progress: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
}

export function ProgressRing({ 
  progress, 
  size = 60, 
  strokeWidth = 4,
  className 
}: ProgressRingProps) {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className={cn('relative', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="rgba(64, 64, 64, 0.3)"
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="rgb(212, 175, 55)"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
          strokeLinecap="round"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="text-sm font-semibold text-platinum-50">
          {Math.round(progress)}%
        </span>
      </div>
    </div>
  );
}