import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Crown, Mail, Lock } from 'lucide-react';
import { useAuthState } from '../../hooks/useAuth';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

export function LoginForm() {
  const { login } = useAuthState();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      await login(email, password);
    } catch (error) {
      console.error('Login failed:', error);
      setError(error instanceof Error ? error.message : 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = async (demoEmail: string, demoPassword: string) => {
    setLoading(true);
    setError('');
    try {
      await login(demoEmail, demoPassword);
    } catch (error) {
      console.error('Demo login failed:', error);
      setError(error instanceof Error ? error.message : 'Demo login failed');
    } finally {
      setLoading(false);
    }
  };

  const demoAccounts = [
    {
      role: 'VVIP Client',
      email: '<EMAIL>',
      password: 'password',
      description: 'Ultra-luxury travel client experience',
      icon: Crown,
      color: 'bg-champagne-gradient'
    },
    {
      role: 'Admin',
      email: '<EMAIL>',
      password: 'password',
      description: 'Travel management console',
      icon: Crown,
      color: 'bg-rose-gradient'
    },
    {
      role: 'Partner',
      email: '<EMAIL>',
      password: 'password',
      description: 'Service provider portal',
      icon: Crown,
      color: 'bg-green-gradient'
    }
  ];

  return (
    <div className="min-h-screen bg-luxury-gradient-light dark:bg-luxury-gradient flex items-center justify-center px-6 transition-colors duration-300">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-md"
      >
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-champagne-gradient rounded-2xl mb-4 shadow-2xl">
            <Crown className="w-8 h-8 text-platinum-900" />
          </div>
          <h1 className="text-4xl font-serif font-bold text-platinum-900 dark:text-platinum-50 mb-2">Opulence</h1>
          <p className="text-champagne-400 font-medium tracking-wider uppercase text-sm">Ultra-Luxury Travel</p>
        </div>

        {/* Login Form */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-white/80 dark:bg-platinum-900/30 backdrop-blur-sm rounded-2xl p-8 border border-champagne-500/20 shadow-2xl transition-colors duration-300"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-champagne-400 w-5 h-5" />
              <input
                type="email"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-platinum-100 dark:bg-platinum-800/50 border border-champagne-500/30 rounded-xl text-platinum-900 dark:text-platinum-50 placeholder-platinum-600 dark:placeholder-platinum-400 focus:outline-none focus:ring-2 focus:ring-champagne-400 focus:border-transparent transition-all duration-200"
                required
              />
            </div>

            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-champagne-400 w-5 h-5" />
              <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-platinum-100 dark:bg-platinum-800/50 border border-champagne-500/30 rounded-xl text-platinum-900 dark:text-platinum-50 placeholder-platinum-600 dark:placeholder-platinum-400 focus:outline-none focus:ring-2 focus:ring-champagne-400 focus:border-transparent transition-all duration-200"
                required
              />
            </div>

            {error && (
              <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-red-400 text-sm">
                {error}
              </div>
            )}

            <Button
              type="submit"
              size="lg"
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Signing In...' : 'Enter Portal'}
            </Button>
          </form>

          {/* Demo Accounts - Quick Access */}
          <div className="mt-8 pt-6 border-t border-champagne-500/20">
            <p className="text-platinum-300 text-sm text-center mb-6">
              🚀 <strong>Quick Demo Access</strong> - Click to login instantly
            </p>
            <div className="space-y-3">
              {demoAccounts.map((account, index) => (
                <motion.button
                  key={account.email}
                  onClick={() => handleDemoLogin(account.email, account.password)}
                  disabled={loading}
                  className="w-full p-4 bg-platinum-800/40 hover:bg-platinum-800/60 border border-champagne-500/20 hover:border-champagne-500/40 rounded-xl transition-all duration-200 text-left group disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 + index * 0.1 }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 ${account.color} rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow`}>
                        <account.icon className="w-5 h-5 text-platinum-900" />
                      </div>
                      <div>
                        <div className="font-semibold text-platinum-50 group-hover:text-champagne-400 transition-colors">
                          {account.role}
                        </div>
                        <div className="text-xs text-platinum-400 group-hover:text-platinum-300 transition-colors">
                          {account.description}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-champagne-400 font-medium">
                        {account.email}
                      </div>
                      <div className="text-xs text-platinum-500 group-hover:text-platinum-400 transition-colors">
                        Click to login
                      </div>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>

            {/* Development Note */}
            <div className="mt-6 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <p className="text-blue-400 text-xs text-center">
                💡 <strong>Development Mode:</strong> All demo accounts use password "password"
              </p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}