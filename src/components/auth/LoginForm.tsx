import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Crown, Mail, Lock } from 'lucide-react';
import { useAuthState } from '../../hooks/useAuth';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

export function LoginForm() {
  const { login } = useAuthState();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      await login(email, password);
    } catch (error) {
      console.error('Login failed:', error);
      setError(error instanceof Error ? error.message : 'Lo<PERSON> failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-luxury-gradient flex items-center justify-center px-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-md"
      >
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-champagne-gradient rounded-2xl mb-4 shadow-2xl">
            <Crown className="w-8 h-8 text-platinum-900" />
          </div>
          <h1 className="text-4xl font-serif font-bold text-platinum-50 mb-2">Opulence</h1>
          <p className="text-champagne-400 font-medium tracking-wider uppercase text-sm">Ultra-Luxury Travel</p>
        </div>

        {/* Login Form */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-platinum-900/30 backdrop-blur-sm rounded-2xl p-8 border border-champagne-500/20 shadow-2xl"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-champagne-400 w-5 h-5" />
              <input
                type="email"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-platinum-800/50 border border-champagne-500/30 rounded-xl text-platinum-50 placeholder-platinum-400 focus:outline-none focus:ring-2 focus:ring-champagne-400 focus:border-transparent transition-all duration-200"
                required
              />
            </div>

            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-champagne-400 w-5 h-5" />
              <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-platinum-800/50 border border-champagne-500/30 rounded-xl text-platinum-50 placeholder-platinum-400 focus:outline-none focus:ring-2 focus:ring-champagne-400 focus:border-transparent transition-all duration-200"
                required
              />
            </div>

            {error && (
              <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-red-400 text-sm">
                {error}
              </div>
            )}

            <Button
              type="submit"
              size="lg"
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Signing In...' : 'Enter Portal'}
            </Button>
          </form>

          {/* Demo Accounts */}
          <div className="mt-8 pt-6 border-t border-champagne-500/20">
            <p className="text-platinum-300 text-sm text-center mb-4">Demo Accounts:</p>
            <div className="space-y-3 text-xs">
              <div className="bg-platinum-800/30 rounded-lg p-3">
                <div className="flex justify-between text-platinum-300 mb-1">
                  <span className="font-medium">VVIP Client:</span>
                  <span className="text-champagne-400"><EMAIL></span>
                </div>
                <div className="text-platinum-400 text-right">Password: password</div>
              </div>
              <div className="bg-platinum-800/30 rounded-lg p-3">
                <div className="flex justify-between text-platinum-300 mb-1">
                  <span className="font-medium">Admin:</span>
                  <span className="text-champagne-400"><EMAIL></span>
                </div>
                <div className="text-platinum-400 text-right">Password: password</div>
              </div>
              <div className="bg-platinum-800/30 rounded-lg p-3">
                <div className="flex justify-between text-platinum-300 mb-1">
                  <span className="font-medium">Partner:</span>
                  <span className="text-champagne-400"><EMAIL></span>
                </div>
                <div className="text-platinum-400 text-right">Password: password</div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}