import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Crown, Mail, Lock, AlertCircle, CheckCircle } from 'lucide-react';
import { useAuthState } from '../../hooks/useAuthBackend';
import { Button } from '../ui/Button';
import { LoadingOverlay } from '../ui/Loading';

export function LoginForm() {
  const { login, loading, error } = useAuthState();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [localError, setLocalError] = useState('');
  const [validationErrors, setValidationErrors] = useState<{
    email?: string;
    password?: string;
  }>({});

  const validateForm = () => {
    const errors: { email?: string; password?: string } = {};
    
    if (!email) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    if (!password) {
      errors.password = 'Password is required';
    } else if (password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLocalError('');
    
    if (!validateForm()) {
      return;
    }

    try {
      await login(email, password);
      // Success - the useAuth hook will handle navigation
    } catch (error) {
      console.error('Login failed:', error);
      setLocalError(error instanceof Error ? error.message : 'Login failed');
    }
  };

  const displayError = error || localError;

  return (
    <>
      <LoadingOverlay 
        isVisible={loading} 
        message="Authenticating your luxury access..." 
        variant="luxury" 
      />
      
      <div className="min-h-screen bg-luxury-gradient flex items-center justify-center px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="w-full max-w-md"
        >
        {/* Logo */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="inline-flex items-center justify-center w-16 h-16 bg-champagne-gradient rounded-2xl mb-4 shadow-2xl"
          >
            <Crown className="w-8 h-8 text-platinum-900" />
          </motion.div>
          <h1 className="text-4xl font-serif font-bold text-platinum-50 mb-2">Opulence</h1>
          <p className="text-champagne-400 font-medium tracking-wider uppercase text-sm">Ultra-Luxury Travel</p>
        </div>

        {/* Login Form */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-platinum-900/30 backdrop-blur-sm rounded-2xl p-8 border border-champagne-500/20 shadow-2xl"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-champagne-400 w-5 h-5" />
              <input
                type="email"
                placeholder="Email address"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (validationErrors.email) {
                    setValidationErrors(prev => ({ ...prev, email: undefined }));
                  }
                }}
                className={`w-full pl-12 pr-4 py-4 bg-platinum-800/50 border rounded-xl text-platinum-50 placeholder-platinum-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 ${
                  validationErrors.email 
                    ? 'border-red-500/50 focus:ring-red-400' 
                    : 'border-champagne-500/30 focus:ring-champagne-400'
                }`}
                disabled={loading}
                required
              />
              <AnimatePresence>
                {validationErrors.email && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute -bottom-6 left-0 text-red-400 text-sm flex items-center gap-1"
                  >
                    <AlertCircle className="w-4 h-4" />
                    {validationErrors.email}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Password Field */}
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-champagne-400 w-5 h-5" />
              <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  if (validationErrors.password) {
                    setValidationErrors(prev => ({ ...prev, password: undefined }));
                  }
                }}
                className={`w-full pl-12 pr-4 py-4 bg-platinum-800/50 border rounded-xl text-platinum-50 placeholder-platinum-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 ${
                  validationErrors.password 
                    ? 'border-red-500/50 focus:ring-red-400' 
                    : 'border-champagne-500/30 focus:ring-champagne-400'
                }`}
                disabled={loading}
                required
              />
              <AnimatePresence>
                {validationErrors.password && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute -bottom-6 left-0 text-red-400 text-sm flex items-center gap-1"
                  >
                    <AlertCircle className="w-4 h-4" />
                    {validationErrors.password}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Error Display */}
            <AnimatePresence>
              {displayError && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  className="bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-red-400 text-sm flex items-center gap-2"
                >
                  <AlertCircle className="w-5 h-5 flex-shrink-0" />
                  <span>{displayError}</span>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Submit Button */}
            <Button
              type="submit"
              size="lg"
              disabled={loading}
              className="w-full relative overflow-hidden"
            >
              <AnimatePresence mode="wait">
                {loading ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="flex items-center gap-2"
                  >
                    <div className="w-5 h-5 border-2 border-platinum-900 border-t-transparent rounded-full animate-spin" />
                    Authenticating...
                  </motion.div>
                ) : (
                  <motion.span
                    key="submit"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    Enter Portal
                  </motion.span>
                )}
              </AnimatePresence>
            </Button>
          </form>

          {/* Development Demo Accounts - Remove in production */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-8 pt-6 border-t border-champagne-500/20">
              <p className="text-platinum-300 text-sm text-center mb-4">Development Demo Accounts:</p>
              <div className="space-y-3 text-xs">
                <div className="bg-platinum-800/30 rounded-lg p-3">
                  <div className="flex justify-between text-platinum-300 mb-1">
                    <span className="font-medium">VVIP Client:</span>
                    <span className="text-champagne-400"><EMAIL></span>
                  </div>
                  <div className="text-platinum-400 text-right">Password: password</div>
                </div>
                <div className="bg-platinum-800/30 rounded-lg p-3">
                  <div className="flex justify-between text-platinum-300 mb-1">
                    <span className="font-medium">Admin:</span>
                    <span className="text-champagne-400"><EMAIL></span>
                  </div>
                  <div className="text-platinum-400 text-right">Password: password</div>
                </div>
                <div className="bg-platinum-800/30 rounded-lg p-3">
                  <div className="flex justify-between text-platinum-300 mb-1">
                    <span className="font-medium">Partner:</span>
                    <span className="text-champagne-400"><EMAIL></span>
                  </div>
                  <div className="text-platinum-400 text-right">Password: password</div>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </motion.div>
    </>
  );
}