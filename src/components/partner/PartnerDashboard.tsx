import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Upload, Image as ImageIcon, Calendar, Clock, DollarSign, CheckCircle, AlertCircle, MessageSquare } from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';

interface Request {
  id: string;
  tripTitle: string;
  clientType: string;
  dates: string;
  guestCount: number;
  requirements: string[];
  budget: string;
  status: 'new' | 'responded' | 'confirmed';
  urgency: 'low' | 'medium' | 'high';
}

const mockRequests: Request[] = [
  {
    id: '1',
    tripTitle: 'Mediterranean Odyssey',
    clientType: 'VVIP Client',
    dates: 'June 15-25, 2024',
    guestCount: 2,
    requirements: ['Oceanview suite', 'Private dining', 'Spa access'],
    budget: '$50,000 - $75,000',
    status: 'new',
    urgency: 'high'
  },
  {
    id: '2',
    tripTitle: 'Alpine Retreat',
    clientType: 'VIP Client',
    dates: 'July 10-17, 2024',
    guestCount: 4,
    requirements: ['Family suite', 'Ski access', 'Kids activities'],
    budget: '$25,000 - $40,000',
    status: 'responded',
    urgency: 'medium'
  }
];

export function PartnerDashboard() {
  const [selectedRequest, setSelectedRequest] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const onDrop = (acceptedFiles: File[]) => {
    setUploadedFiles(prev => [...prev, ...acceptedFiles]);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    }
  });

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-rose-500/20 text-rose-400';
      case 'medium': return 'bg-champagne-500/20 text-champagne-400';
      case 'low': return 'bg-green-500/20 text-green-400';
      default: return 'bg-platinum-600/20 text-platinum-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-500/20 text-blue-400';
      case 'responded': return 'bg-champagne-500/20 text-champagne-400';
      case 'confirmed': return 'bg-green-500/20 text-green-400';
      default: return 'bg-platinum-600/20 text-platinum-300';
    }
  };

  return (
    <div className="min-h-screen bg-luxury-gradient-light dark:bg-luxury-gradient transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-serif font-bold text-platinum-50 mb-2">Partner Dashboard</h1>
          <p className="text-platinum-300">Manage your luxury experience requests and media</p>
        </motion.div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {[
            { label: 'Active Requests', value: '12', icon: AlertCircle, color: 'bg-blue-500' },
            { label: 'Response Rate', value: '98%', icon: CheckCircle, color: 'bg-green-500' },
            { label: 'Avg Response Time', value: '2.3h', icon: Clock, color: 'bg-amber-500' },
            { label: 'This Month Revenue', value: '$245K', icon: DollarSign, color: 'bg-purple-500' }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-600 text-sm font-medium">{stat.label}</p>
                    <p className="text-2xl font-bold text-platinum-50 mt-1">{stat.value}</p>
                  </div>
                  <div className={`w-10 h-10 rounded-xl ${stat.color} flex items-center justify-center`}>
                    <stat.icon className="w-5 h-5 text-white" />
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Requests List */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <h2 className="text-xl font-serif font-bold text-platinum-50 mb-6">Experience Requests</h2>
              <div className="space-y-4">
                {mockRequests.map((request) => (
                  <motion.div
                    key={request.id}
                    whileHover={{ scale: 1.01 }}
                    className={`p-4 border-2 rounded-xl cursor-pointer transition-all ${
                      selectedRequest === request.id 
                        ? 'border-champagne-400 bg-champagne-500/10' 
                        : 'border-platinum-600/30 hover:border-champagne-500/50'
                    }`}
                    onClick={() => setSelectedRequest(request.id)}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-semibold text-platinum-50">{request.tripTitle}</h3>
                        <p className="text-sm text-platinum-300">{request.clientType}</p>
                      </div>
                      <div className="flex space-x-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(request.urgency)}`}>
                          {request.urgency.toUpperCase()}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                          {request.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm text-platinum-300 mb-3">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-2 text-champagne-400" />
                        {request.dates}
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="w-4 h-4 mr-2 text-champagne-400" />
                        {request.budget}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {request.requirements.map((req, index) => (
                        <span key={index} className="px-2 py-1 bg-platinum-700/30 text-platinum-300 rounded-lg text-xs">
                          {req}
                        </span>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </Card>
          </div>

          {/* Response Form */}
          <div>
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-platinum-50 mb-4">Respond to Request</h3>
              {selectedRequest ? (
                <div className="space-y-4">
                  <Input label="Availability" placeholder="Confirm dates available..." />
                  <Input label="Pricing" placeholder="Quote your rate..." />
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Experience Details
                    </label>
                    <textarea
                      className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent resize-none"
                      rows={4}
                      placeholder="Describe your offering in detail..."
                    />
                  </div>
                  
                  {/* Media Upload */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Upload Experience Media
                    </label>
                    <div
                      {...getRootProps()}
                      className={`border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-colors ${
                        isDragActive ? 'border-amber-400 bg-amber-50' : 'border-slate-300 hover:border-amber-400'
                      }`}
                    >
                      <input {...getInputProps()} />
                      <ImageIcon className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                      <p className="text-slate-600">
                        {isDragActive ? 'Drop images here...' : 'Drag & drop images or click to upload'}
                      </p>
                    </div>
                    
                    {uploadedFiles.length > 0 && (
                      <div className="mt-4 grid grid-cols-2 gap-2">
                        {uploadedFiles.map((file, index) => (
                          <div key={index} className="relative">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={file.name}
                              className="w-full h-20 object-cover rounded-lg"
                            />
                            <button
                              onClick={() => setUploadedFiles(files => files.filter((_, i) => i !== index))}
                              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <Button className="w-full">
                    Submit Response
                  </Button>
                </div>
              ) : (
                <div className="text-center py-8">
                  <MessageSquare className="w-12 h-12 text-platinum-400 mx-auto mb-4" />
                  <p className="text-platinum-400">Select a request to respond</p>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}