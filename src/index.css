@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Enhanced typography base styles */
  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variant-numeric: oldstyle-nums;
  }
  
  body {
    font-family: 'Inter', system-ui, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
  
  /* Luxury typography enhancements */
  .font-serif {
    font-family: 'Playfair Display', serif;
    font-feature-settings: "kern", "liga", "clig", "calt";
  }
  
  .font-sans {
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: "kern", "liga", "clig", "calt", "cv02", "cv03", "cv04", "cv11";
  }
}

@layer components {
  /* Luxury number formatting */
  .luxury-numbers {
    font-variant-numeric: proportional-nums;
    font-feature-settings: "tnum" 0, "pnum" 1;
  }
  
  .tabular-numbers {
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum" 1, "pnum" 0;
  }
  
  /* Enhanced text selection */
  ::selection {
    background-color: rgba(212, 175, 55, 0.2);
    color: rgb(245, 245, 245);
  }
  
  /* Smooth scrolling for luxury experience */
  html {
    scroll-behavior: smooth;
  }
  
  /* Custom scrollbar for luxury feel */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: rgba(23, 23, 23, 0.5);
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(212, 175, 55, 0.3);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(212, 175, 55, 0.5);
  }
}

@layer utilities {
  /* Text balance for better typography */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Optical alignment for luxury layouts */
  .optical-align {
    text-align: left;
    hanging-punctuation: first last;
  }
  
  /* Enhanced letter spacing for luxury feel */
  .tracking-luxury {
    letter-spacing: 0.025em;
  }
  
  .tracking-ultra-wide {
    letter-spacing: 0.15em;
  }
  
  /* Background size utilities for animations */
  .bg-size-200 {
    background-size: 200% 100%;
  }
  
  .bg-pos-0 {
    background-position: 0% 50%;
  }
  
  .bg-pos-100 {
    background-position: 100% 50%;
  }
  
  /* Perspective for 3D effects */
  .perspective-1000 {
    perspective: 1000px;
  }
  
  .preserve-3d {
    transform-style: preserve-3d;
  }
  
  /* Backdrop blur variations */
  .backdrop-blur-luxury {
    backdrop-filter: blur(20px) saturate(180%);
  }
}
