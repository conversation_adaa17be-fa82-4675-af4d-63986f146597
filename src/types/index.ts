export type UserRole = 'vvip' | 'partner' | 'admin';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  dietary: string[];
  loyaltyPrograms: string[];
  travelStyle: string;
  preferredBrands: string[];
  likes: string[];
  dislikes: string[];
}

export interface Trip {
  id: string;
  clientId: string;
  title: string;
  description: string;
  destination: string; // Primary destination
  destinations: string[]; // All destinations in the trip
  startDate: Date;
  endDate: Date;
  status: 'draft' | 'proposed' | 'confirmed' | 'pending' | 'in-progress' | 'completed';
  totalCost: number;
  totalPrice: number; // Alias for totalCost for API compatibility
  guestCount: number;
  heroImage: string;
  images: string[];
  itinerary: ItineraryDay[];
}

export interface ItineraryDay {
  id: string;
  day: number;
  date: Date;
  title: string;
  description: string;
  activities: Activity[];
  accommodation?: Accommodation;
  images: string[];
}

export interface Activity {
  id: string;
  partnerId: string;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  cost: number;
  images: string[];
  location: string;
  partnerName: string;
}

export interface Accommodation {
  id: string;
  partnerId: string;
  name: string;
  type: string;
  cost: number;
  images: string[];
  amenities: string[];
  partnerName: string;
}

export interface Partner {
  id: string;
  name: string;
  type: 'hotel' | 'restaurant' | 'activity' | 'transport';
  location: string;
  qualityScore: number;
  responseTime: number;
  avatar: string;
  description: string;
  media: string[];
}

export interface Quote {
  id: string;
  tripId: string;
  clientId: string;
  managerId: string;
  version: number;
  status: 'draft' | 'sent' | 'reviewed' | 'approved' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
  notes?: string;
  changes?: ChangeRequest[];
}

export interface ChangeRequest {
  id: string;
  dayId: string;
  activityId?: string;
  type: 'modification' | 'addition' | 'removal';
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
}