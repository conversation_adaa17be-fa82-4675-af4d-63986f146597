import { Trip, User } from '../../types';
import { mockTrips, mockUsers } from '../../data/mockData';

// Mock API responses that match our API client expectations
export class MockAPIServer {
  private static instance: MockAPIServer;
  private trips: Trip[] = [];
  private users: User[] = [];

  constructor() {
    // Initialize with mock data and ensure proper typing
    this.trips = mockTrips.map(trip => ({
      ...trip,
      description: `Experience the ultimate luxury in ${trip.destination}`,
      destinations: [trip.destination],
      totalPrice: trip.totalCost,
      guestCount: 2,
      heroImage: trip.images[0] || 'https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=800',
      startDate: new Date(trip.startDate),
      endDate: new Date(trip.endDate || new Date(trip.startDate).getTime() + 7 * 24 * 60 * 60 * 1000),
    }));
    
    this.users = mockUsers || [];
  }

  static getInstance(): MockAPIServer {
    if (!MockAPIServer.instance) {
      MockAPIServer.instance = new MockAPIServer();
    }
    return MockAPIServer.instance;
  }

  // Simulate network delay
  private async delay(ms: number = 300 + Math.random() * 700): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Mock API endpoints
  async getTrips(clientId?: string): Promise<{ data: Trip[] }> {
    await this.delay();
    
    let filteredTrips = this.trips;
    if (clientId) {
      filteredTrips = this.trips.filter(trip => trip.clientId === clientId);
    }
    
    return { data: filteredTrips };
  }

  async getTrip(tripId: string): Promise<{ data: Trip | null }> {
    await this.delay();
    
    const trip = this.trips.find(t => t.id === tripId);
    return { data: trip || null };
  }

  async getTripStats(clientId: string): Promise<{ 
    data: {
      activeTrips: number;
      upcomingTrips: number;
      totalDestinations: number;
      totalExperiences: number;
    }
  }> {
    await this.delay();
    
    const userTrips = this.trips.filter(trip => trip.clientId === clientId);
    const now = new Date();
    
    const activeTrips = userTrips.filter(trip => 
      trip.status === 'confirmed' || trip.status === 'in-progress'
    ).length;
    
    const upcomingTrips = userTrips.filter(trip => 
      new Date(trip.startDate) > now
    ).length;
    
    const totalDestinations = new Set(
      userTrips.flatMap(trip => trip.destinations)
    ).size;
    
    const totalExperiences = userTrips.reduce((sum, trip) => 
      sum + (trip.itinerary?.length || 0), 0
    );
    
    return {
      data: {
        activeTrips,
        upcomingTrips,
        totalDestinations,
        totalExperiences
      }
    };
  }

  async getFeaturedDestinations(): Promise<{
    data: Array<{
      id: string;
      title: string;
      image: string;
      description: string;
      price: string;
    }>
  }> {
    await this.delay();
    
    const featured = [
      {
        id: 'patagonia-expedition',
        title: 'Patagonia Expedition',
        image: 'https://images.pexels.com/photos/1619317/pexels-photo-1619317.jpeg?auto=compress&cs=tinysrgb&w=800',
        description: 'Private wilderness adventure',
        price: 'From $85,000'
      },
      {
        id: 'japanese-cultural',
        title: 'Japanese Cultural Immersion',
        image: 'https://images.pexels.com/photos/2614818/pexels-photo-2614818.jpeg?auto=compress&cs=tinysrgb&w=800',
        description: 'Traditional ryokans & private ceremonies',
        price: 'From $95,000'
      },
      {
        id: 'african-safari',
        title: 'African Safari Premier',
        image: 'https://images.pexels.com/photos/631317/pexels-photo-631317.jpeg?auto=compress&cs=tinysrgb&w=800',
        description: 'Ultra-luxury wildlife experience',
        price: 'From $120,000'
      }
    ];
    
    return { data: featured };
  }

  async createTrip(tripData: Partial<Trip>): Promise<{ data: Trip }> {
    await this.delay();
    
    const newTrip: Trip = {
      id: `trip_${Date.now()}`,
      clientId: tripData.clientId || '',
      title: tripData.title || 'New Luxury Experience',
      description: tripData.description || 'A bespoke luxury travel experience',
      destination: tripData.destination || 'Morocco',
      destinations: tripData.destinations || [tripData.destination || 'Morocco'],
      startDate: tripData.startDate || new Date(),
      endDate: tripData.endDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      status: tripData.status || 'draft',
      totalCost: tripData.totalCost || 50000,
      totalPrice: tripData.totalPrice || tripData.totalCost || 50000,
      guestCount: tripData.guestCount || 2,
      heroImage: tripData.heroImage || 'https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=800',
      images: tripData.images || [],
      itinerary: tripData.itinerary || []
    };
    
    this.trips.push(newTrip);
    return { data: newTrip };
  }

  async updateTrip(tripId: string, updates: Partial<Trip>): Promise<{ data: Trip | null }> {
    await this.delay();
    
    const tripIndex = this.trips.findIndex(t => t.id === tripId);
    if (tripIndex === -1) {
      return { data: null };
    }
    
    this.trips[tripIndex] = { ...this.trips[tripIndex], ...updates };
    return { data: this.trips[tripIndex] };
  }

  async deleteTrip(tripId: string): Promise<{ success: boolean }> {
    await this.delay();
    
    const tripIndex = this.trips.findIndex(t => t.id === tripId);
    if (tripIndex === -1) {
      return { success: false };
    }
    
    this.trips.splice(tripIndex, 1);
    return { success: true };
  }

  async createTripInquiry(data: {
    clientId: string;
    context: string;
    preferences?: Record<string, any>;
  }): Promise<{ data: { inquiryId: string } }> {
    await this.delay();
    
    // In a real app, this would create an inquiry record
    const inquiryId = `inquiry_${Date.now()}`;
    
    console.log('Trip inquiry created:', { inquiryId, ...data });
    
    return { data: { inquiryId } };
  }
}

// Global instance
export const mockAPIServer = MockAPIServer.getInstance();