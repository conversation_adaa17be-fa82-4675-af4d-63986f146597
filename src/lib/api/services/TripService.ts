import { ApiClient } from '../client';
import { Trip, CreateTripRequest, UpdateTripRequest } from '../../types';
import { validateApiResponse } from '../validation';
import { TripSchema, TripListSchema } from '../schemas/tripSchemas';

export class TripService extends ApiClient {
  /**
   * Get trips for a specific client with luxury caching
   */
  async getClientTrips(clientId: string): Promise<Trip[]> {
    const response = await this.request<Trip[]>(`/trips?clientId=${clientId}&include=itinerary,accommodation,activities`);
    return validateApiResponse(response.data, TripListSchema);
  }

  /**
   * Get a specific trip with full details for timeline view
   */
  async getTripById(tripId: string): Promise<Trip> {
    const response = await this.request<Trip>(`/trips/${tripId}?include=itinerary,accommodation,activities,partners`);
    return validateApiResponse(response.data, TripSchema);
  }

  /**
   * Get trip statistics for dashboard quick stats
   */
  async getTripStats(clientId: string): Promise<{
    activeTrips: number;
    upcomingTrips: number;
    totalDestinations: number;
    totalExperiences: number;
  }> {
    const response = await this.request<any>(`/trips/stats?clientId=${clientId}`);
    return response.data;
  }

  /**
   * Get featured destinations for discovery section
   */
  async getFeaturedDestinations(limit: number = 3): Promise<{
    title: string;
    image: string;
    description: string;
    price: string;
    id: string;
  }[]> {
    const response = await this.request<any>(`/destinations/featured?limit=${limit}`);
    return response.data;
  }

  /**
   * Create a new trip inquiry from AI chat context
   */
  async createTripInquiry(data: {
    clientId: string;
    context: string;
    preferences?: Record<string, any>;
  }): Promise<{ inquiryId: string }> {
    const response = await this.request<any>('/trips/inquiries', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    return response.data;
  }
}

export const tripService = new TripService(process.env.VITE_API_BASE_URL || 'http://localhost:3001/api');