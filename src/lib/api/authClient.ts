import { refreshAuthToken } from '../../hooks/useAuthBackend';

interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

class AuthenticatedApiClient {
  private baseURL: string;

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    let token = localStorage.getItem('access_token');
    
    // Try to refresh token if it doesn't exist
    if (!token) {
      token = await refreshAuthToken();
    }

    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    if (response.status === 401) {
      // Try to refresh token
      const newToken = await refreshAuthToken();
      if (!newToken) {
        // Redirect to login or emit logout event
        window.location.href = '/login';
        throw new Error('Authentication required');
      }
      // Retry the original request would go here
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  async get<T>(endpoint: string): Promise<T> {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'GET',
      headers,
    });

    const result = await this.handleResponse<T>(response);
    return result.data;
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    const result = await this.handleResponse<T>(response);
    return result.data;
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(data),
    });

    const result = await this.handleResponse<T>(response);
    return result.data;
  }

  async delete<T>(endpoint: string): Promise<T> {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'DELETE',
      headers,
    });

    const result = await this.handleResponse<T>(response);
    return result.data;
  }
}

export const apiClient = new AuthenticatedApiClient();