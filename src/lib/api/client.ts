import { TokenSecurity } from '../security/tokenSecurity';
import { mockAPIServer } from './mockServer';
import { errorHandler, handleApiError, handleNetworkError, ErrorType } from '../errors/errorHandler';

export interface ApiResponse<T = any> {
  status: number;
  data?: T;
  error?: ApiError;
  metadata: {
    requestId: string;
    timestamp: string;
    processingTime: number;
    cached?: boolean;
  };
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  documentation?: string;
}

export class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private useMockServer: boolean;

  constructor(baseURL: string = '/api', useMockServer: boolean = true) {
    this.baseURL = baseURL;
    this.useMockServer = useMockServer && process.env.NODE_ENV === 'development';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = localStorage.getItem('access_token');
    if (!token) return {};

    // Check if token needs refresh
    if (TokenSecurity.needsRefresh(token)) {
      try {
        const newToken = await this.refreshToken();
        if (newToken) {
          return { 'Authorization': `Bearer ${newToken}` };
        }
      } catch (error) {
        console.error('Token refresh failed:', error);
        TokenSecurity.clearTokens();
        window.location.href = '/login';
        return {};
      }
    }

    return { 'Authorization': `Bearer ${token}` };
  }

  private async refreshToken(): Promise<string | null> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) return null;

    const response = await fetch(`${this.baseURL}/auth/refresh`, {
      method: 'POST',
      headers: this.defaultHeaders,
      body: JSON.stringify({ refreshToken }),
    });

    if (response.ok) {
      const { accessToken, refreshToken: newRefreshToken } = await response.json();
      TokenSecurity.storeTokenSecurely(accessToken, newRefreshToken);
      return accessToken;
    }

    return null;
  }

  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const startTime = Date.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Use mock server in development
    if (this.useMockServer) {
      return this.handleMockRequest<T>(endpoint, options, requestId, startTime);
    }

    try {
      const authHeaders = await this.getAuthHeaders();
      const url = `${this.baseURL}${endpoint}`;

      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.defaultHeaders,
          ...authHeaders,
          'X-Request-ID': requestId,
          'X-Client-Version': '1.0.0',
          ...options.headers,
        },
      });

      const processingTime = Date.now() - startTime;

      if (!response.ok) {
        // Handle API errors with proper error reporting
        const apiError = handleApiError(response, requestId);
        
        let errorDetails;
        try {
          const responseData = await response.json();
          errorDetails = responseData.error;
        } catch {
          // If response is not JSON, use default error
          errorDetails = {
            code: `HTTP_${response.status}`,
            message: this.getErrorMessageFromStatus(response.status),
          };
        }

        return {
          status: response.status,
          error: errorDetails || {
            code: 'HTTP_ERROR',
            message: `HTTP ${response.status}: ${response.statusText}`,
          },
          metadata: {
            requestId,
            timestamp: new Date().toISOString(),
            processingTime,
          },
        };
      }

      const responseData = await response.json();

      // Validate response format for production APIs
      if (!this.useMockServer && !this.isValidApiResponse(responseData)) {
        const validationError = errorHandler.createError(
          ErrorType.API_ERROR,
          'INVALID_RESPONSE_FORMAT',
          'Server returned invalid response format',
          { responseData },
          { endpoint, requestId }
        );
        errorHandler.handleError(validationError);
        
        return {
          status: 500,
          error: {
            code: 'INVALID_RESPONSE_FORMAT',
            message: 'Server returned invalid response format',
          },
          metadata: {
            requestId,
            timestamp: new Date().toISOString(),
            processingTime,
          },
        };
      }

      return {
        status: response.status,
        data: responseData.data || responseData,
        metadata: {
          requestId,
          timestamp: new Date().toISOString(),
          processingTime,
          cached: responseData.metadata?.cached,
        },
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      // Handle network errors with proper error reporting
      if (error instanceof TypeError && error.message.includes('fetch')) {
        const networkError = handleNetworkError(error, requestId);
        
        return {
          status: 0,
          error: {
            code: 'NETWORK_ERROR',
            message: 'Unable to connect to our services. Please check your internet connection.',
          },
          metadata: {
            requestId,
            timestamp: new Date().toISOString(),
            processingTime,
          },
        };
      }

      // Handle other errors
      const unknownError = errorHandler.createError(
        ErrorType.UNKNOWN_ERROR,
        'REQUEST_FAILED',
        error instanceof Error ? error.message : 'Request failed',
        { originalError: error },
        { endpoint, requestId }
      );
      errorHandler.handleError(unknownError);
      
      return {
        status: 0,
        error: {
          code: 'REQUEST_FAILED',
          message: error instanceof Error ? error.message : 'Request failed',
        },
        metadata: {
          requestId,
          timestamp: new Date().toISOString(),
          processingTime,
        },
      };
    }
  }

  private getErrorMessageFromStatus(status: number): string {
    switch (status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'Authentication required. Please log in to continue.';
      case 403:
        return 'Access denied. You don\'t have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Our team has been notified and is working to resolve this.';
      case 502:
      case 503:
      case 504:
        return 'Service temporarily unavailable. Please try again in a few moments.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  private isValidApiResponse(data: any): boolean {
    // For mock responses, we're more lenient
    if (this.useMockServer) {
      return true;
    }
    
    // For real API responses, validate structure
    return (
      typeof data === 'object' &&
      data !== null &&
      (data.data !== undefined || data.error !== undefined)
    );
  }

  private async handleMockRequest<T>(
    endpoint: string,
    options: RequestInit,
    requestId: string,
    startTime: number
  ): Promise<ApiResponse<T>> {
    try {
      let mockResponse: any;
      const method = options.method || 'GET';
      const body = options.body ? JSON.parse(options.body as string) : undefined;

      // Route to appropriate mock server method
      if (endpoint.startsWith('/trips/stats')) {
        const clientId = new URL(`http://localhost${endpoint}`).searchParams.get('clientId');
        mockResponse = await mockAPIServer.getTripStats(clientId || '');
      } else if (endpoint.startsWith('/trips/inquiries') && method === 'POST') {
        mockResponse = await mockAPIServer.createTripInquiry(body);
      } else if (endpoint.startsWith('/trips/') && method === 'GET') {
        const tripId = endpoint.split('/trips/')[1];
        mockResponse = await mockAPIServer.getTrip(tripId);
      } else if (endpoint.startsWith('/trips') && method === 'POST') {
        mockResponse = await mockAPIServer.createTrip(body);
      } else if (endpoint.startsWith('/trips') && method === 'PATCH') {
        const tripId = endpoint.split('/trips/')[1];
        mockResponse = await mockAPIServer.updateTrip(tripId, body);
      } else if (endpoint.startsWith('/trips') && method === 'DELETE') {
        const tripId = endpoint.split('/trips/')[1];
        mockResponse = await mockAPIServer.deleteTrip(tripId);
      } else if (endpoint.startsWith('/trips')) {
        const clientId = new URL(`http://localhost${endpoint}`).searchParams.get('clientId');
        mockResponse = await mockAPIServer.getTrips(clientId || undefined);
      } else if (endpoint.startsWith('/destinations/featured')) {
        mockResponse = await mockAPIServer.getFeaturedDestinations();
      } else {
        throw new Error(`Mock endpoint not implemented: ${method} ${endpoint}`);
      }

      const processingTime = Date.now() - startTime;

      return {
        status: 200,
        data: mockResponse.data,
        metadata: {
          requestId,
          timestamp: new Date().toISOString(),
          processingTime,
          cached: false,
        },
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      return {
        status: 500,
        error: {
          code: 'MOCK_ERROR',
          message: error instanceof Error ? error.message : 'Mock server error',
        },
        metadata: {
          requestId,
          timestamp: new Date().toISOString(),
          processingTime,
        },
      };
    }
  }

  // Convenience methods
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// Global API client instance
export const apiClient = new ApiClient();