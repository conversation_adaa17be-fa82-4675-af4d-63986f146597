import { cn } from './utils';

// Typography component classes for consistent luxury styling
export const typography = {
  // Display text for hero sections and major headings
  display: {
    '2xl': 'text-display-2xl font-serif text-platinum-900 dark:text-platinum-50',
    'xl': 'text-display-xl font-serif text-platinum-900 dark:text-platinum-50',
    'lg': 'text-display-lg font-serif text-platinum-900 dark:text-platinum-50',
    'md': 'text-display-md font-serif text-platinum-900 dark:text-platinum-50',
    'sm': 'text-display-sm font-serif text-platinum-900 dark:text-platinum-50',
  },
  
  // Headings for sections and subsections
  heading: {
    'xl': 'text-heading-xl font-sans font-semibold text-platinum-900 dark:text-platinum-50',
    'lg': 'text-heading-lg font-sans font-semibold text-platinum-900 dark:text-platinum-50',
    'md': 'text-heading-md font-sans font-medium text-platinum-800 dark:text-platinum-50',
    'sm': 'text-heading-sm font-sans font-medium text-platinum-700 dark:text-platinum-50',
  },
  
  // Body text for content
  body: {
    'xl': 'text-body-xl font-sans text-slate-800 dark:text-platinum-200',
    'lg': 'text-body-lg font-sans text-slate-800 dark:text-platinum-200',
    'md': 'text-body-md font-sans text-slate-700 dark:text-platinum-300',
    'sm': 'text-body-sm font-sans text-slate-700 dark:text-platinum-300',
  },
  
  // Price display with luxury emphasis
  price: {
    'hero': 'text-price-hero font-serif text-champagne-400 font-bold',
    'lg': 'text-price-lg font-serif text-champagne-400 font-semibold',
    'md': 'text-price-md font-sans text-champagne-400 font-semibold',
    'sm': 'text-price-sm font-sans text-champagne-400 font-medium',
  },
  
  // Captions and metadata
  caption: {
    'lg': 'text-caption-lg font-sans text-slate-600 dark:text-platinum-400 uppercase tracking-wider',
    'md': 'text-caption-md font-sans text-slate-600 dark:text-platinum-400 uppercase tracking-wider',
    'sm': 'text-caption-sm font-sans text-slate-500 dark:text-platinum-500 uppercase tracking-wider',
  },
  
  // Special emphasis styles
  emphasis: {
    'luxury': 'text-champagne-500 dark:text-champagne-400 font-medium',
    'premium': 'text-rose-500 dark:text-rose-400 font-medium',
    'exclusive': 'text-platinum-800 dark:text-platinum-200 font-semibold bg-champagne-500/10 px-2 py-1 rounded-md',
    'vip': 'text-champagne-600 dark:text-champagne-300 font-bold tracking-wide uppercase text-sm',
  },
} as const;

// Content density and spacing utilities
export const spacing = {
  // Section spacing
  section: {
    'tight': 'space-y-6',
    'normal': 'space-y-8',
    'relaxed': 'space-y-12',
    'loose': 'space-y-16',
    'luxury': 'space-y-20',
  },
  
  // Content spacing
  content: {
    'tight': 'space-y-2',
    'normal': 'space-y-4',
    'relaxed': 'space-y-6',
    'loose': 'space-y-8',
  },
  
  // Padding for content containers
  container: {
    'tight': 'p-4',
    'normal': 'p-6',
    'relaxed': 'p-8',
    'luxury': 'p-12',
  },
} as const;

// Visual hierarchy utilities
export const hierarchy = {
  // Card hierarchy
  card: {
    'primary': 'bg-white/95 dark:bg-platinum-900/95 border border-champagne-500/20 shadow-2xl',
    'secondary': 'bg-white/90 dark:bg-platinum-800/90 border border-platinum-600/30 shadow-xl',
    'tertiary': 'bg-white/80 dark:bg-platinum-700/80 border border-platinum-500/20 shadow-lg',
  },
  
  // Border emphasis
  border: {
    'luxury': 'border-champagne-500/30',
    'premium': 'border-rose-400/30',
    'standard': 'border-platinum-600/30',
    'subtle': 'border-platinum-700/30',
  },
  
  // Background emphasis
  background: {
    'luxury': 'bg-champagne-500/5',
    'premium': 'bg-rose-400/5',
    'elevated': 'bg-platinum-200/50 dark:bg-platinum-800/50',
    'subtle': 'bg-platinum-100/30 dark:bg-platinum-900/30',
  },
} as const;

// Helper function to create typography classes
export function createTypographyClass(
  category: keyof typeof typography,
  size: string,
  additionalClasses?: string
): string {
  const baseClass = typography[category]?.[size as keyof typeof typography[typeof category]];
  return cn(baseClass, additionalClasses);
}

// Helper function for responsive typography
export function responsiveTypography(
  mobile: string,
  tablet?: string,
  desktop?: string
): string {
  const classes = [mobile];
  if (tablet) classes.push(`md:${tablet}`);
  if (desktop) classes.push(`lg:${desktop}`);
  return cn(...classes);
}

// Luxury content formatting utilities
export const luxuryFormatting = {
  // Price emphasis with visual hierarchy
  priceEmphasis: (price: string, context: 'hero' | 'card' | 'inline' = 'card') => {
    const baseClasses = {
      hero: typography.price.hero,
      card: typography.price.lg,
      inline: typography.price.md,
    };
    return cn(baseClasses[context], 'tracking-tight');
  },
  
  // Status badges with luxury styling
  statusBadge: (status: string, variant: 'luxury' | 'premium' | 'standard' = 'standard') => {
    const variants = {
      luxury: 'bg-champagne-500/20 text-champagne-400 border border-champagne-500/30',
      premium: 'bg-rose-400/20 text-rose-400 border border-rose-400/30',
      standard: 'bg-platinum-600/20 text-platinum-300 border border-platinum-600/30',
    };
    return cn(
      'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
      variants[variant]
    );
  },
  
  // Metadata display with proper hierarchy
  metadata: (label: string, value: string) => ({
    label: cn(typography.caption.md, 'text-platinum-500'),
    value: cn(typography.body.lg, 'text-platinum-200 font-medium'),
  }),
};