import { QueryClient } from '@tanstack/react-query';
import { errorHandler, ErrorType } from './errors/errorHandler';

// Create a client with luxury-appropriate configuration and error handling
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Luxury UX: Keep data fresh but don't over-fetch
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
      
      // Retry configuration for reliability
      retry: (failureCount, error: any) => {
        // Don't retry on authentication errors
        if (error?.message?.includes('401') || error?.message?.includes('403')) {
          return false;
        }
        // Don't retry on client errors (4xx)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 3 times for server errors
        return failureCount < 3;
      },
      
      // Retry delay with exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // Refetch configuration for luxury experience
      refetchOnWindowFocus: true, // Keep data current when user returns
      refetchOnReconnect: true, // Refresh when connection restored
      refetchOnMount: true, // Always get fresh data on mount
      
      // Network mode for offline capability
      networkMode: 'online',
    },
    mutations: {
      // Retry mutations for better reliability
      retry: (failureCount, error: any) => {
        // Don't retry client errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 2;
      },
      
      // Network mode for mutations
      networkMode: 'online',
    },
  },
});

// Global error handling setup
const handleGlobalError = (error: any, context?: any) => {
  console.error('React Query error:', error);
  
  // Create structured error for reporting
  const errorType = getErrorTypeFromMessage(error?.message || '');
  const appError = errorHandler.createError(
    errorType,
    'REACT_QUERY_ERROR',
    error?.message || 'Unknown query error',
    { 
      originalError: error,
      context 
    },
    { 
      component: 'ReactQuery' 
    }
  );
  
  // Handle authentication errors globally
  if (errorType === ErrorType.AUTHENTICATION_ERROR) {
    queryClient.clear();
    setTimeout(() => {
      window.location.href = '/login';
    }, 1000);
  }
  
  // Report error (but not network errors to avoid spam)
  if (errorType !== ErrorType.NETWORK_ERROR) {
    errorHandler.handleError(appError);
  }
};

function getErrorTypeFromMessage(message: string): ErrorType {
  if (message.includes('fetch') || message.includes('network')) {
    return ErrorType.NETWORK_ERROR;
  }
  if (message.includes('401') || message.includes('unauthorized')) {
    return ErrorType.AUTHENTICATION_ERROR;
  }
  if (message.includes('403') || message.includes('forbidden')) {
    return ErrorType.AUTHORIZATION_ERROR;
  }
  if (message.includes('400') || message.includes('validation')) {
    return ErrorType.VALIDATION_ERROR;
  }
  if (message.includes('500') || message.includes('server')) {
    return ErrorType.API_ERROR;
  }
  return ErrorType.UNKNOWN_ERROR;
}

// Prefetch commonly used data for luxury UX
export const prefetchCommonData = async (clientId?: string) => {
  if (clientId) {
    // Prefetch user's trips
    await queryClient.prefetchQuery({
      queryKey: ['trips', clientId],
      queryFn: async () => {
        // This would be implemented by the useTrips hook
        return [];
      },
      staleTime: 5 * 60 * 1000,
    });
    
    // Prefetch trip stats
    await queryClient.prefetchQuery({
      queryKey: ['tripStats', clientId],
      queryFn: async () => {
        return {
          activeTrips: 0,
          upcomingTrips: 0,
          totalDestinations: 0,
          totalExperiences: 0,
        };
      },
      staleTime: 1 * 60 * 1000,
    });
  }
  
  // Prefetch featured destinations (global)
  await queryClient.prefetchQuery({
    queryKey: ['featuredDestinations'],
    queryFn: async () => {
      return [];
    },
    staleTime: 30 * 60 * 1000,
  });
};

// Utility function to invalidate all user-specific data
export const invalidateUserData = (clientId: string) => {
  queryClient.invalidateQueries({ queryKey: ['trips', clientId] });
  queryClient.invalidateQueries({ queryKey: ['tripStats', clientId] });
  queryClient.invalidateQueries({ queryKey: ['user', clientId] });
};

// Utility function to clear all cached data (for logout)
export const clearAllCache = () => {
  queryClient.clear();
};