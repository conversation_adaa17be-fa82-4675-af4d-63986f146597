// Comprehensive error handling system for backend integration

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface AppError {
  type: ErrorType;
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  requestId?: string;
  userId?: string;
  context?: Record<string, any>;
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorQueue: AppError[] = [];
  private isOnline = navigator.onLine;

  private constructor() {
    this.setupNetworkListeners();
    this.setupUnhandledErrorListeners();
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  private setupNetworkListeners() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  private setupUnhandledErrorListeners() {
    window.addEventListener('error', (event) => {
      this.handleError({
        type: ErrorType.UNKNOWN_ERROR,
        code: 'UNHANDLED_ERROR',
        message: event.error?.message || 'An unhandled error occurred',
        details: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack
        },
        timestamp: new Date()
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: ErrorType.UNKNOWN_ERROR,
        code: 'UNHANDLED_PROMISE_REJECTION',
        message: event.reason?.message || 'Unhandled promise rejection',
        details: {
          reason: event.reason,
          stack: event.reason?.stack
        },
        timestamp: new Date()
      });
    });
  }

  handleError(error: AppError): void {
    // Log error locally
    console.error('Error handled:', error);

    // Add to queue for reporting
    this.errorQueue.push(error);

    // Try to report immediately if online
    if (this.isOnline) {
      this.flushErrorQueue();
    }

    // Show user-friendly notification for critical errors
    if (this.isCriticalError(error)) {
      this.showErrorNotification(error);
    }
  }

  handleApiError(response: Response, requestId?: string): AppError {
    const error: AppError = {
      type: this.getErrorTypeFromStatus(response.status),
      code: `HTTP_${response.status}`,
      message: this.getErrorMessageFromStatus(response.status),
      details: {
        status: response.status,
        statusText: response.statusText,
        url: response.url
      },
      timestamp: new Date(),
      requestId
    };

    this.handleError(error);
    return error;
  }

  handleNetworkError(error: Error, requestId?: string): AppError {
    const appError: AppError = {
      type: ErrorType.NETWORK_ERROR,
      code: 'NETWORK_FAILURE',
      message: 'Unable to connect to our services. Please check your internet connection.',
      details: {
        originalError: error.message,
        stack: error.stack
      },
      timestamp: new Date(),
      requestId
    };

    this.handleError(appError);
    return appError;
  }

  private getErrorTypeFromStatus(status: number): ErrorType {
    if (status === 401) return ErrorType.AUTHENTICATION_ERROR;
    if (status === 403) return ErrorType.AUTHORIZATION_ERROR;
    if (status >= 400 && status < 500) return ErrorType.VALIDATION_ERROR;
    if (status >= 500) return ErrorType.API_ERROR;
    return ErrorType.UNKNOWN_ERROR;
  }

  private getErrorMessageFromStatus(status: number): string {
    switch (status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'Authentication required. Please log in to continue.';
      case 403:
        return 'Access denied. You don\'t have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Our team has been notified and is working to resolve this.';
      case 502:
      case 503:
      case 504:
        return 'Service temporarily unavailable. Please try again in a few moments.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  private isCriticalError(error: AppError): boolean {
    return error.type === ErrorType.AUTHENTICATION_ERROR ||
           error.type === ErrorType.API_ERROR ||
           (error.type === ErrorType.NETWORK_ERROR && !this.isOnline);
  }

  private showErrorNotification(error: AppError): void {
    // This would integrate with your toast notification system
    // For now, we'll use a simple console log
    console.warn('Critical error notification:', error.message);
    
    // In a real implementation:
    // toastService.showError(error.message, {
    //   duration: 5000,
    //   action: error.type === ErrorType.NETWORK_ERROR ? 'Retry' : undefined
    // });
  }

  private async flushErrorQueue(): Promise<void> {
    if (this.errorQueue.length === 0 || !this.isOnline) {
      return;
    }

    const errorsToReport = [...this.errorQueue];
    this.errorQueue = [];

    try {
      await fetch('/api/errors/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          errors: errorsToReport,
          clientInfo: {
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString()
          }
        })
      });
    } catch (reportingError) {
      // If reporting fails, add errors back to queue
      this.errorQueue.unshift(...errorsToReport);
      console.warn('Failed to report errors to backend:', reportingError);
    }
  }

  // Utility method for components to create standardized errors
  createError(
    type: ErrorType,
    code: string,
    message: string,
    details?: any,
    context?: Record<string, any>
  ): AppError {
    return {
      type,
      code,
      message,
      details,
      context,
      timestamp: new Date()
    };
  }

  // Method to clear error queue (useful for testing)
  clearErrorQueue(): void {
    this.errorQueue = [];
  }
}

// Convenience functions for common error scenarios
export const errorHandler = ErrorHandler.getInstance();

export const handleApiError = (response: Response, requestId?: string) => 
  errorHandler.handleApiError(response, requestId);

export const handleNetworkError = (error: Error, requestId?: string) => 
  errorHandler.handleNetworkError(error, requestId);

export const createError = (
  type: ErrorType,
  code: string,
  message: string,
  details?: any,
  context?: Record<string, any>
) => errorHandler.createError(type, code, message, details, context);