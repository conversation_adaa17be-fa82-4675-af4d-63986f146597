// Token security utilities for VVIP client protection

interface TokenPayload {
  sub: string; // user ID
  email: string;
  role: 'vvip' | 'admin' | 'partner';
  iat: number; // issued at
  exp: number; // expires at
}

export class TokenSecurity {
  // Validate token structure and expiration
  static validateToken(token: string): boolean {
    try {
      const payload = this.decodeToken(token);
      const now = Math.floor(Date.now() / 1000);
      
      // Check if token is expired
      if (payload.exp < now) {
        return false;
      }
      
      // Check if token was issued too far in the past (max 24 hours)
      if (now - payload.iat > 86400) {
        return false;
      }
      
      return true;
    } catch {
      return false;
    }
  }

  // Decode JWT token (client-side validation only)
  static decodeToken(token: string): TokenPayload {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid token format');
    }
    
    const payload = JSON.parse(atob(parts[1]));
    return payload as TokenPayload;
  }

  // Check if token needs refresh (expires in next 5 minutes)
  static needsRefresh(token: string): boolean {
    try {
      const payload = this.decodeToken(token);
      const now = Math.floor(Date.now() / 1000);
      const fiveMinutes = 5 * 60;
      
      return payload.exp - now < fiveMinutes;
    } catch {
      return true;
    }
  }

  // Secure token storage with additional encryption for VVIP clients
  static storeTokenSecurely(token: string, refreshToken: string): void {
    // In production, consider additional encryption for VVIP client tokens
    localStorage.setItem('access_token', token);
    localStorage.setItem('refresh_token', refreshToken);
    
    // Store token metadata for security monitoring
    localStorage.setItem('token_stored_at', Date.now().toString());
  }

  // Clear all authentication data
  static clearTokens(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('token_stored_at');
    
    // Clear any cached user data
    sessionStorage.clear();
  }

  // Check for suspicious token activity
  static detectSuspiciousActivity(): boolean {
    const storedAt = localStorage.getItem('token_stored_at');
    if (!storedAt) return false;
    
    const storedTime = parseInt(storedAt);
    const now = Date.now();
    const hoursSinceStored = (now - storedTime) / (1000 * 60 * 60);
    
    // Flag if token has been stored for more than 24 hours without refresh
    return hoursSinceStored > 24;
  }
}

// Rate limiting for authentication attempts
export class AuthRateLimit {
  private static attempts: Map<string, { count: number; lastAttempt: number }> = new Map();
  private static readonly MAX_ATTEMPTS = 5;
  private static readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

  static canAttemptLogin(email: string): boolean {
    const key = email.toLowerCase();
    const record = this.attempts.get(key);
    
    if (!record) return true;
    
    const now = Date.now();
    const timeSinceLastAttempt = now - record.lastAttempt;
    
    // Reset attempts after lockout duration
    if (timeSinceLastAttempt > this.LOCKOUT_DURATION) {
      this.attempts.delete(key);
      return true;
    }
    
    return record.count < this.MAX_ATTEMPTS;
  }

  static recordFailedAttempt(email: string): void {
    const key = email.toLowerCase();
    const record = this.attempts.get(key) || { count: 0, lastAttempt: 0 };
    
    record.count += 1;
    record.lastAttempt = Date.now();
    
    this.attempts.set(key, record);
  }

  static recordSuccessfulLogin(email: string): void {
    const key = email.toLowerCase();
    this.attempts.delete(key);
  }

  static getRemainingLockoutTime(email: string): number {
    const key = email.toLowerCase();
    const record = this.attempts.get(key);
    
    if (!record || record.count < this.MAX_ATTEMPTS) return 0;
    
    const now = Date.now();
    const timeSinceLastAttempt = now - record.lastAttempt;
    const remainingTime = this.LOCKOUT_DURATION - timeSinceLastAttempt;
    
    return Math.max(0, remainingTime);
  }
}