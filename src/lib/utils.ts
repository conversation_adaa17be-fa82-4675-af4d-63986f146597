import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: number, options?: {
  style?: 'compact' | 'full' | 'elegant';
  showCents?: boolean;
}): string {
  const { style = 'elegant', showCents = false } = options || {};
  
  const baseOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: showCents ? 2 : 0,
    maximumFractionDigits: showCents ? 2 : 0,
  };

  switch (style) {
    case 'compact':
      return new Intl.NumberFormat('en-US', {
        ...baseOptions,
        notation: 'compact',
        compactDisplay: 'short',
      }).format(amount);
    
    case 'full':
      return new Intl.NumberFormat('en-US', baseOptions).format(amount);
    
    case 'elegant':
    default:
      // Custom elegant formatting for luxury pricing
      const formatted = new Intl.NumberFormat('en-US', baseOptions).format(amount);
      // Add thin spaces for better readability in luxury context
      return formatted.replace(/,/g, '\u2009'); // Thin space instead of comma
  }
}

export function formatLuxuryNumber(num: number, options?: {
  style?: 'compact' | 'full';
  precision?: number;
}): string {
  const { style = 'full', precision = 0 } = options || {};
  
  if (style === 'compact') {
    return new Intl.NumberFormat('en-US', {
      notation: 'compact',
      compactDisplay: 'short',
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    }).format(num);
  }
  
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  }).format(num).replace(/,/g, '\u2009'); // Thin space for elegance
}

export function formatPriceRange(min: number, max: number): string {
  const minFormatted = formatCurrency(min, { style: 'elegant' });
  const maxFormatted = formatCurrency(max, { style: 'elegant' });
  return `${minFormatted} – ${maxFormatted}`;
}

export function formatDuration(days: number): string {
  if (days === 1) return '1 day';
  if (days < 7) return `${days} days`;
  
  const weeks = Math.floor(days / 7);
  const remainingDays = days % 7;
  
  if (remainingDays === 0) {
    return weeks === 1 ? '1 week' : `${weeks} weeks`;
  }
  
  const weeksText = weeks === 1 ? '1 week' : `${weeks} weeks`;
  const daysText = remainingDays === 1 ? '1 day' : `${remainingDays} days`;
  
  return `${weeksText}, ${daysText}`;
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  }).format(date);
}

export function formatDateRange(startDate: Date, endDate: Date): string {
  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
}