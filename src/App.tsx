import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useAuthState } from './hooks/useAuth';
import { Header } from './components/layout/Header';
import { LoginForm } from './components/auth/LoginForm';
import { ClientDashboard } from './components/client/ClientDashboard';
import { PartnerDashboard } from './components/partner/PartnerDashboard';
import { AdminDashboard } from './components/admin/AdminDashboard';
import { QuoteBuilder } from './components/admin/QuoteBuilder';
import { TripTimeline } from './components/client/TripTimeline';
import { AIChat } from './components/client/AIChat';
import { RoutesIndex } from './components/dev/RoutesIndex';
import { TypographyShowcase } from './components/dev/TypographyShowcase';
import { MicroInteractionsDemo } from './components/dev/MicroInteractionsDemo';
import { LuxuryCardsShowcase } from './components/dev/LuxuryCardsShowcase';
import { NetworkTester } from './components/dev/NetworkTester';
import { BackendIntegrationTest } from './components/dev/BackendIntegrationTest';
import { queryClient } from './lib/queryClient';
import { ErrorBoundary } from './components/ui/ErrorBoundary';

// Wrapper component for TripTimeline to handle route params
function TripTimelineWrapper() {
  // This would normally get trip data from URL params and pass to TripTimeline
  // For now, we'll redirect to dashboard since this needs proper implementation
  return <Navigate to="/client" replace />;
}

// Wrapper component for AIChat to handle standalone chat page
function AIChatWrapper() {
  const [isOpen, setIsOpen] = React.useState(true);
  
  const handleClose = () => {
    // Navigate back to dashboard when chat is closed
    setIsOpen(false);
  };

  if (!isOpen) {
    return <Navigate to="/client" replace />;
  }

  return (
    <div className="min-h-screen bg-luxury-gradient">
      <AIChat 
        isOpen={isOpen} 
        onClose={handleClose} 
        context="Standalone chat session" 
      />
    </div>
  );
}

// Wrapper component for QuoteBuilder to handle standalone page
function QuoteBuilderWrapper() {
  const handleClose = () => {
    // Navigate back to admin dashboard when closed
    return <Navigate to="/admin" replace />;
  };

  return <QuoteBuilder onClose={handleClose} />;
}

function AppContent() {
  const { user, loading } = useAuthState();

  if (loading) {
    return (
      <div className="min-h-screen bg-luxury-gradient flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-champagne-400 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  const getDashboardRoute = (role: string) => {
    switch (role) {
      case 'vvip': return '/client';
      case 'admin': return '/admin';
      case 'partner': return '/partner';
      default: return '/login';
    }
  };

  return (
    <Router>
      <div className="min-h-screen bg-luxury-gradient">
        {user && <Header />}
        <Routes>
          {/* Development Routes */}
          <Route path="/routes" element={<RoutesIndex />} />
          <Route path="/typography-showcase" element={<TypographyShowcase />} />
          <Route path="/micro-interactions-demo" element={<MicroInteractionsDemo />} />
          <Route path="/luxury-cards-showcase" element={<LuxuryCardsShowcase />} />
          <Route path="/backend-integration-test" element={<BackendIntegrationTest />} />
          
          {/* Authentication */}
          <Route path="/login" element={<LoginForm />} />
          
          {/* Client/VVIP Routes */}
          <Route path="/client" element={user?.role === 'vvip' ? <ClientDashboard /> : <Navigate to="/login" />} />
          <Route path="/client/trip/:tripId" element={user?.role === 'vvip' ? <TripTimelineWrapper /> : <Navigate to="/login" />} />
          <Route path="/client/chat" element={user?.role === 'vvip' ? <AIChatWrapper /> : <Navigate to="/login" />} />
          
          {/* Admin Routes */}
          <Route path="/admin" element={user?.role === 'admin' ? <AdminDashboard /> : <Navigate to="/login" />} />
          <Route path="/admin/quote-builder" element={user?.role === 'admin' ? <QuoteBuilderWrapper /> : <Navigate to="/login" />} />
          
          {/* Partner Routes */}
          <Route path="/partner" element={user?.role === 'partner' ? <PartnerDashboard /> : <Navigate to="/login" />} />
          
          {/* Default Route */}
          <Route path="/" element={<Navigate to={user ? getDashboardRoute(user.role) : "/login"} replace />} />
          
          {/* Catch All */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

function App() {
  return (
    <ErrorBoundary level="critical">
      <QueryClientProvider client={queryClient}>
        <ErrorBoundary level="page">
          <AppContent />
        </ErrorBoundary>
        <ReactQueryDevtools initialIsOpen={false} />
        <NetworkTester />
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;