# Loading Components Backend Integration Guide

## Overview

The new Loading component system provides sophisticated loading states that are essential for backend integration. This guide shows how to integrate these components with real API calls while maintaining the luxury UX standards.

## Loading Component Usage Patterns

### 1. **LoadingSpinner** - For Button States and Inline Loading

```tsx
// ✅ CORRECT: Button loading state
<Button disabled={isLoading}>
  {isLoading ? (
    <LoadingSpinner size="sm" variant="elegant" className="mr-2" />
  ) : (
    <Plus className="w-4 h-4 mr-2" />
  )}
  {isLoading ? 'Creating...' : 'Create Trip'}
</Button>

// ✅ CORRECT: Inline loading for small components
{isLoadingPartners ? (
  <LoadingSpinner size="md" variant="luxury" />
) : (
  <PartnerList partners={partners} />
)}
```

### 2. **LoadingSkeleton** - For Content Placeholders

```tsx
// ✅ CORRECT: Card skeleton while loading
{isLoading ? (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {Array.from({ length: 6 }).map((_, index) => (
      <LoadingSkeleton key={index} variant="card" className="h-80" />
    ))}
  </div>
) : (
  <TripGrid trips={trips} />
)}

// ✅ CORRECT: Text skeleton for content
{isLoading ? (
  <LoadingSkeleton variant="text" lines={3} className="max-w-2xl" />
) : (
  <div className="prose">
    <p>{content}</p>
  </div>
)}
```

### 3. **LoadingOverlay** - For Full-Screen Operations

```tsx
// ✅ CORRECT: Authentication overlay
<LoadingOverlay 
  isVisible={isAuthenticating} 
  message="Authenticating your luxury access..." 
  variant="luxury" 
/>

// ✅ CORRECT: Trip creation overlay
<LoadingOverlay 
  isVisible={isCreatingTrip} 
  message="Crafting your luxury experience..." 
  variant="luxury" 
/>
```

### 4. **LoadingDots** - For Chat and Real-time Features

```tsx
// ✅ CORRECT: AI chat typing indicator
{isAITyping && (
  <div className="flex items-center space-x-2 text-platinum-400">
    <span className="text-sm">AI Concierge is typing</span>
    <LoadingDots size="sm" />
  </div>
)}
```

## React Query Integration Patterns

### 1. **Query Loading States**

```tsx
const TripComponent = ({ tripId }: { tripId: string }) => {
  const { data: trip, isLoading, error, refetch } = useTrip(tripId);

  if (isLoading) {
    return <LoadingSkeleton variant="card" className="h-96" />;
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400 mb-4">{error.message}</p>
        <Button onClick={() => refetch()} variant="outline">
          <LoadingSpinner size="sm" className="mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return <TripDetails trip={trip} />;
};
```

### 2. **Mutation Loading States**

```tsx
const CreateTripForm = () => {
  const createTripMutation = useCreateTrip();

  const handleSubmit = async (data: TripFormData) => {
    try {
      await createTripMutation.mutateAsync(data);
      // Success handling
    } catch (error) {
      // Error handling
    }
  };

  return (
    <>
      <LoadingOverlay 
        isVisible={createTripMutation.isLoading} 
        message="Creating your luxury experience..." 
      />
      
      <form onSubmit={handleSubmit}>
        {/* Form fields */}
        <Button 
          type="submit" 
          disabled={createTripMutation.isLoading}
        >
          {createTripMutation.isLoading ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Creating...
            </>
          ) : (
            'Create Trip'
          )}
        </Button>
      </form>
    </>
  );
};
```

## API Client Integration

### 1. **Error Handling with Loading States**

```tsx
// lib/api/errorHandler.ts
export const handleApiError = (error: ApiError, context: string) => {
  console.error(`API Error in ${context}:`, error);
  
  // Return user-friendly error messages
  switch (error.code) {
    case 'NETWORK_ERROR':
      return 'Connection issue. Please check your internet and try again.';
    case 'AUTHENTICATION_FAILED':
      return 'Your session has expired. Please log in again.';
    case 'VALIDATION_ERROR':
      return error.details ? 
        Object.values(error.details).join(', ') : 
        'Please check your input and try again.';
    default:
      return 'An unexpected error occurred. Our team has been notified.';
  }
};
```

### 2. **Loading State Management**

```tsx
// hooks/api/useApiCall.ts
export const useApiCall = <T>(
  apiCall: () => Promise<ApiResponse<T>>,
  options: {
    onSuccess?: (data: T) => void;
    onError?: (error: string) => void;
    loadingMessage?: string;
  } = {}
) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<T | null>(null);

  const execute = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiCall();
      
      if (response.error) {
        const errorMessage = handleApiError(response.error, 'API Call');
        setError(errorMessage);
        options.onError?.(errorMessage);
      } else {
        setData(response.data!);
        options.onSuccess?.(response.data!);
      }
    } catch (err) {
      const errorMessage = 'Network error occurred';
      setError(errorMessage);
      options.onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return { execute, isLoading, error, data };
};
```

## Mobile Responsiveness

### 1. **Touch-Optimized Loading States**

```tsx
// Mobile-specific loading patterns
const MobileLoadingSpinner = ({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) => {
  return (
    <div className="flex items-center justify-center min-h-[44px]">
      <LoadingSpinner 
        size={size} 
        variant="luxury" 
        className="touch-none" // Prevent touch interactions
      />
    </div>
  );
};

// Mobile skeleton with proper spacing
const MobileCardSkeleton = () => {
  return (
    <LoadingSkeleton 
      variant="card" 
      className="h-64 md:h-80 rounded-xl" // Responsive height
    />
  );
};
```

### 2. **Progressive Loading for Mobile**

```tsx
const MobileOptimizedList = () => {
  const [page, setPage] = useState(1);
  const { data, isLoading, hasNextPage, fetchNextPage } = useInfiniteTrips();

  return (
    <div className="space-y-4">
      {data?.pages.map((page, pageIndex) => (
        <div key={pageIndex} className="space-y-4">
          {page.trips.map((trip) => (
            <TripCard key={trip.id} trip={trip} />
          ))}
        </div>
      ))}
      
      {isLoading && (
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <MobileCardSkeleton key={index} />
          ))}
        </div>
      )}
      
      {hasNextPage && (
        <Button 
          onClick={() => fetchNextPage()}
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? (
            <LoadingSpinner size="sm" className="mr-2" />
          ) : null}
          Load More
        </Button>
      )}
    </div>
  );
};
```

## Performance Optimization

### 1. **Lazy Loading with Suspense**

```tsx
// Lazy load heavy components
const HeavyTripDetails = lazy(() => import('./HeavyTripDetails'));

const TripDetailsWrapper = ({ tripId }: { tripId: string }) => {
  return (
    <Suspense 
      fallback={<LoadingSkeleton variant="card" className="h-96" />}
    >
      <HeavyTripDetails tripId={tripId} />
    </Suspense>
  );
};
```

### 2. **Optimistic Updates with Loading States**

```tsx
const useOptimisticTripUpdate = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: updateTrip,
    onMutate: async (variables) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries(['trip', variables.tripId]);
      
      // Snapshot previous value
      const previousTrip = queryClient.getQueryData(['trip', variables.tripId]);
      
      // Optimistically update
      queryClient.setQueryData(['trip', variables.tripId], (old: Trip) => ({
        ...old,
        ...variables.updates,
      }));
      
      return { previousTrip };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousTrip) {
        queryClient.setQueryData(['trip', variables.tripId], context.previousTrip);
      }
    },
    onSettled: (data, error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries(['trip', variables.tripId]);
    },
  });
};
```

## Testing Loading States

### 1. **Unit Tests for Loading Components**

```tsx
// __tests__/LoadingSpinner.test.tsx
describe('LoadingSpinner', () => {
  it('renders luxury variant with crown icon', () => {
    render(<LoadingSpinner variant="luxury" />);
    expect(screen.getByTestId('crown-icon')).toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    render(<LoadingSpinner size="lg" />);
    expect(screen.getByRole('status')).toHaveClass('w-8 h-8');
  });
});
```

### 2. **Integration Tests with API Mocking**

```tsx
// __tests__/TripList.test.tsx
describe('TripList', () => {
  it('shows loading skeleton while fetching trips', async () => {
    // Mock API delay
    server.use(
      rest.get('/api/trips', (req, res, ctx) => {
        return res(ctx.delay(1000), ctx.json({ data: mockTrips }));
      })
    );

    render(<TripList />);
    
    // Should show loading skeleton
    expect(screen.getAllByTestId('loading-skeleton')).toHaveLength(6);
    
    // Should show trips after loading
    await waitFor(() => {
      expect(screen.getByText('Morocco Imperial Cities')).toBeInTheDocument();
    });
  });
});
```

## Backend API Requirements

### 1. **Response Format Standards**

```typescript
// All API responses should follow this format
interface ApiResponse<T> {
  status: number;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    requestId: string;
    timestamp: string;
    processingTime: number;
  };
}
```

### 2. **Loading-Friendly Endpoints**

```typescript
// Endpoints should support pagination for better loading UX
GET /api/trips?page=1&limit=20&cursor=abc123

// Should return:
{
  "status": 200,
  "data": {
    "trips": [...],
    "pagination": {
      "hasNext": true,
      "nextCursor": "def456",
      "totalCount": 150
    }
  }
}
```

## Next Steps for Backend Integration

1. **Set up React Query Provider** in your main App component
2. **Replace mock data calls** with the new API hooks
3. **Add error boundaries** for graceful error handling
4. **Implement retry logic** for network failures
5. **Add offline support** with cached data
6. **Monitor performance** with loading time metrics

This loading system ensures that your VVIP clients always have a premium experience, even during data loading and network issues.