{"enabled": true, "name": "Mobile Responsiveness Checker", "description": "Automatically check mobile responsiveness when UI components are modified", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/components/**/*.tsx", "src/pages/**/*.tsx", "tailwind.config.js", "src/index.css"]}, "then": {"type": "askAgent", "prompt": "📱 **MOBILE RESPONSIVENESS VALIDATION** 📱\n\nA UI component or styling file has been modified. Please perform comprehensive mobile responsiveness analysis:\n\n## 1. **Responsive Design Validation**\nCheck for:\n- [ ] Mobile-first CSS approach (base styles for mobile)\n- [ ] Proper Tailwind breakpoint usage (sm:, md:, lg:, xl:, 2xl:)\n- [ ] Flexible layouts that adapt to different screen sizes\n- [ ] Appropriate use of responsive utilities\n- [ ] No fixed widths that break on mobile\n\n## 2. **Touch Optimization**\nVerify:\n- [ ] Touch targets are at least 44px (11 in Tailwind)\n- [ ] Adequate spacing between interactive elements\n- [ ] Hover states have touch equivalents\n- [ ] Swipe gestures are implemented where appropriate\n- [ ] No hover-dependent functionality\n\n## 3. **Typography Responsiveness**\nEnsure:\n- [ ] Text scales appropriately across devices\n- [ ] Line heights work well on mobile\n- [ ] Reading width is comfortable on all screens\n- [ ] Font sizes are legible without zooming\n- [ ] Text doesn't overflow containers\n\n## 4. **Layout Adaptability**\nValidate:\n- [ ] Grid layouts collapse appropriately on mobile\n- [ ] Navigation adapts to mobile patterns\n- [ ] Content hierarchy remains clear on small screens\n- [ ] Images and media are responsive\n- [ ] Forms are mobile-friendly\n\n## 5. **Performance Considerations**\nCheck:\n- [ ] No unnecessary CSS for mobile devices\n- [ ] Images are optimized for different screen densities\n- [ ] Animations perform well on mobile\n- [ ] Bundle size impact is minimal\n- [ ] Critical CSS is prioritized\n\n## 6. **Luxury Experience Preservation**\nEnsure:\n- [ ] Luxury aesthetic is maintained on mobile\n- [ ] Spacing and typography feel premium\n- [ ] Interactions feel smooth and refined\n- [ ] Loading states are elegant\n- [ ] Error states are gracefully handled\n\n## 7. **Cross-Device Testing Recommendations**\nSuggest testing on:\n- [ ] iPhone (various sizes)\n- [ ] Android devices (various sizes)\n- [ ] Tablets (portrait and landscape)\n- [ ] Different browsers\n- [ ] Various network conditions\n\n## Action Required:\nProvide specific recommendations for improving mobile responsiveness while maintaining the ultra-luxury experience. Flag any potential issues and suggest solutions."}}