{"enabled": true, "name": "Test Runner", "description": "Automatically run tests when code changes are made to ensure quality", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.test.ts", "src/**/*.test.tsx"]}, "then": {"type": "runCommand", "command": "npm run test -- --watchAll=false --passWithNoTests --silent", "description": "Run all tests to ensure code quality"}}