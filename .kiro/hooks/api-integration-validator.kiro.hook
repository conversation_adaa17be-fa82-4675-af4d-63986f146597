{"enabled": true, "name": "API Integration Validator", "description": "Validate API integration patterns and ensure partner-friendly implementations", "version": "1", "when": {"type": "userTriggered", "patterns": ["src/api/**/*.ts", "src/hooks/**/*.ts", "src/lib/api/**/*.ts", "src/services/**/*.ts"]}, "then": {"type": "askAgent", "prompt": "🔗 **API INTEGRATION VALIDATION** 🔗\n\nAPI-related code has been modified. Please perform comprehensive API integration analysis:\n\n## 1. **RESTful API Standards**\nVerify:\n- [ ] Proper HTTP methods (GET, POST, PUT, DELETE, PATCH)\n- [ ] Consistent URL structure and naming conventions\n- [ ] Appropriate HTTP status codes\n- [ ] Standard request/response formats\n- [ ] Proper use of headers\n\n## 2. **Partner API Friendliness**\nCheck for:\n- [ ] Clear and consistent API contracts\n- [ ] Comprehensive error messages with actionable details\n- [ ] Proper pagination for large datasets\n- [ ] Filtering and sorting capabilities\n- [ ] Versioning strategy implementation\n\n## 3. **Authentication & Security**\nEnsure:\n- [ ] Secure authentication methods (JWT, API keys)\n- [ ] Proper authorization checks\n- [ ] Rate limiting implementation\n- [ ] Input validation and sanitization\n- [ ] HTTPS enforcement\n\n## 4. **Error Handling Excellence**\nValidate:\n- [ ] Consistent error response format\n- [ ] Meaningful error codes and messages\n- [ ] Proper error categorization (4xx vs 5xx)\n- [ ] Retry mechanisms for transient failures\n- [ ] Circuit breaker patterns for external services\n\n## 5. **Data Validation & Types**\nCheck:\n- [ ] TypeScript interfaces for all API contracts\n- [ ] Input validation using schemas (Zod, Joi, etc.)\n- [ ] Output validation to ensure contract compliance\n- [ ] Proper handling of optional vs required fields\n- [ ] Data transformation and normalization\n\n## 6. **Performance & Scalability**\nVerify:\n- [ ] Efficient database queries\n- [ ] Proper caching strategies\n- [ ] Async/await usage for non-blocking operations\n- [ ] Connection pooling for database access\n- [ ] Response compression where appropriate\n\n## 7. **Real-Time Capabilities**\nEnsure:\n- [ ] WebSocket implementation for real-time updates\n- [ ] Event-driven architecture patterns\n- [ ] Proper handling of connection failures\n- [ ] Efficient data synchronization\n- [ ] Conflict resolution strategies\n\n## 8. **Monitoring & Observability**\nCheck for:\n- [ ] Comprehensive logging\n- [ ] Performance metrics collection\n- [ ] Health check endpoints\n- [ ] Request tracing capabilities\n- [ ] Alert mechanisms for failures\n\n## 9. **Documentation Readiness**\nValidate:\n- [ ] Code is self-documenting with clear naming\n- [ ] JSDoc comments for public APIs\n- [ ] OpenAPI/Swagger compatibility\n- [ ] Example requests and responses\n- [ ] Integration guides preparation\n\n## 10. **Partner Integration Patterns**\nEnsure:\n- [ ] Webhook support for event notifications\n- [ ] Bulk operations for efficiency\n- [ ] Idempotency for safe retries\n- [ ] Graceful degradation strategies\n- [ ] Partner-specific customization support\n\n## Action Required:\nProvide specific recommendations for improving API integration capabilities, ensuring partner-friendliness, and maintaining luxury platform standards."}}