{"enabled": true, "name": "Backend Integration Helper", "description": "Assist with backend integration when replacing mock data with real API calls", "version": "1", "when": {"type": "userTriggered", "patterns": ["src/data/mockData.ts", "src/hooks/**/*.ts", "src/components/**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "🔗 **BACKEND INTEGRATION ASSISTANCE** 🔗\n\nCode changes detected that may require backend integration. Please analyze:\n\n## 1. **Mock Data Analysis**\nIf mockData.ts was modified:\n- [ ] Identify which API endpoints need to be created\n- [ ] Map data structures to expected backend schemas\n- [ ] Suggest proper REST or GraphQL endpoint designs\n- [ ] Recommend authentication requirements\n- [ ] Plan data validation strategies\n\n## 2. **Component Integration**\nFor component updates:\n- [ ] Add proper loading states for API calls\n- [ ] Implement error handling and user feedback\n- [ ] Ensure graceful fallbacks for failed requests\n- [ ] Add retry mechanisms where appropriate\n- [ ] Optimize for luxury user experience\n\n## 3. **React Query Patterns**\nFor hooks and data fetching:\n- [ ] Implement proper useQuery/useMutation patterns\n- [ ] Add caching strategies for performance\n- [ ] Handle optimistic updates where needed\n- [ ] Implement proper error boundaries\n- [ ] Add background refetching logic\n\n## 4. **TypeScript Integration**\nVerify:\n- [ ] Frontend interfaces match backend schemas\n- [ ] Proper type safety for API responses\n- [ ] Error type definitions\n- [ ] Request/response type consistency\n- [ ] Generic type usage for reusability\n\n## 5. **Authentication & Security**\nEnsure:\n- [ ] Proper JWT token handling\n- [ ] Role-based access control\n- [ ] Secure API endpoint calls\n- [ ] HTTPS enforcement\n- [ ] Input sanitization\n\n## Action Required:\nProvide specific guidance for transitioning from mock data to real backend integration while maintaining luxury UX standards."}}