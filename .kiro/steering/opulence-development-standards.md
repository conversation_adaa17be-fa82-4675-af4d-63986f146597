# Opulence Development Standards

## Project Overview

Opulence is an ultra-luxury travel platform serving VVIP clients, luxury service partners, and travel administrators. All development must maintain the highest standards of quality, security, and user experience appropriate for ultra-high-net-worth individuals and luxury service providers.

## Code Quality Standards

### TypeScript Requirements
- All new code MUST be written in TypeScript with strict type checking enabled
- Interfaces and types MUST be properly defined for all data structures
- Generic types SHOULD be used where appropriate to maintain type safety
- Any types are FORBIDDEN except in very specific migration scenarios

### Component Architecture
- Components MUST follow the established pattern: UI components in `/components/ui/`, feature components in `/components/[feature]/`
- All components MUST be functional components using React hooks
- Custom hooks MUST be created for reusable logic and placed in `/hooks/`
- Components MUST be properly exported and imported using named exports

### Styling Guidelines
- ALL styling MUST use Tailwind CSS classes - no custom CSS files
- Color scheme MUST use the established luxury palette: platinum, champagne, and dark gradients
- Animations MUST use Framer Motion for consistency
- Responsive design MUST be mobile-first with proper breakpoints
- Accessibility MUST be considered with proper ARIA labels and semantic HTML

## Luxury UX Standards

### Visual Design
- All interfaces MUST convey luxury and sophistication
- Typography MUST use the established font hierarchy with serif fonts for headings
- Spacing MUST be generous and allow content to breathe
- Images MUST be high-quality and properly optimized
- Loading states MUST be elegant with luxury-appropriate animations

### User Experience
- All interactions MUST feel premium and responsive
- Error states MUST be handled gracefully with helpful messaging
- Forms MUST include proper validation with clear feedback
- Navigation MUST be intuitive for non-technical luxury clients
- Performance MUST be optimized for fast loading times

## Data Handling Standards

### API Integration
- All API calls MUST include proper error handling and loading states
- Data fetching MUST use React Query or similar for caching and synchronization
- API responses MUST be properly typed with TypeScript interfaces
- Authentication tokens MUST be handled securely with automatic refresh
- Rate limiting MUST be respected with appropriate retry logic

### State Management
- Local state MUST use React hooks (useState, useReducer)
- Global state MUST use Context API or Redux Toolkit when needed
- State updates MUST be immutable and properly typed
- Side effects MUST be handled in useEffect with proper cleanup
- Form state MUST use React Hook Form with Zod validation

## Security Requirements

### Authentication & Authorization
- All routes MUST implement proper role-based access control
- JWT tokens MUST be stored securely and refreshed automatically
- Sensitive operations MUST require additional verification
- Session management MUST handle timeouts gracefully
- User data MUST be validated on both client and server sides

### Data Protection
- All sensitive data MUST be encrypted in transit and at rest
- PII (Personally Identifiable Information) MUST be handled according to GDPR standards
- Payment information MUST never be stored locally
- File uploads MUST be validated and sanitized
- API keys and secrets MUST never be committed to version control

## Testing Standards

### Unit Testing
- All utility functions MUST have unit tests with 100% coverage
- React components MUST have tests for key functionality and edge cases
- Custom hooks MUST be tested with React Testing Library
- API integration functions MUST be tested with mocked responses
- Test files MUST be co-located with source files using `.test.ts` or `.test.tsx` extensions

### Integration Testing
- Critical user flows MUST have integration tests
- API endpoints MUST be tested with real backend integration
- Authentication flows MUST be thoroughly tested
- Payment processing MUST have comprehensive test coverage
- Error scenarios MUST be tested and handled appropriately

## Performance Standards

### Frontend Performance
- Bundle size MUST be optimized with code splitting and lazy loading
- Images MUST be optimized and served through CDN
- API calls MUST be cached appropriately to reduce server load
- Animations MUST be performant and not block the main thread
- Core Web Vitals MUST meet Google's recommended thresholds

### Backend Performance
- API responses MUST return within 200ms for standard operations
- Database queries MUST be optimized with proper indexing
- Caching strategies MUST be implemented for frequently accessed data
- File uploads MUST be processed efficiently with progress indicators
- Real-time features MUST handle concurrent users without degradation

## Deployment Standards

### Environment Management
- Development, staging, and production environments MUST be properly configured
- Environment variables MUST be used for all configuration settings
- Database migrations MUST be versioned and reversible
- CI/CD pipelines MUST include automated testing and deployment
- Monitoring and logging MUST be implemented for all environments

### Production Requirements
- SSL certificates MUST be properly configured and renewed automatically
- Backup strategies MUST ensure data recovery within defined RTOs
- Monitoring MUST include uptime, performance, and error tracking
- Security scanning MUST be performed regularly
- Documentation MUST be maintained and kept up-to-date

## Morocco Content Standards

### Cultural Authenticity
- All Moroccan content MUST be culturally accurate and respectful
- Partner information MUST reflect real establishments and services
- Pricing MUST be market-accurate for luxury travel in Morocco
- Images MUST be authentic and high-quality representations
- Descriptions MUST use appropriate cultural terminology

### Luxury Positioning
- All experiences MUST be positioned as ultra-luxury offerings
- Pricing MUST reflect the premium nature of VVIP services
- Service descriptions MUST emphasize exclusivity and personalization
- Partner quality scores MUST reflect actual luxury standards
- Itineraries MUST showcase unique, high-end experiences

## File Organization

### Project Structure
```
src/
├── components/
│   ├── ui/           # Reusable UI components
│   ├── auth/         # Authentication components
│   ├── client/       # VVIP client components
│   ├── admin/        # Admin dashboard components
│   ├── partner/      # Partner portal components
│   └── layout/       # Layout and navigation components
├── hooks/            # Custom React hooks
├── lib/              # Utility functions and configurations
├── types/            # TypeScript type definitions
├── data/             # Mock data and constants
└── assets/           # Static assets and images
```

### Naming Conventions
- Components MUST use PascalCase (e.g., `ClientDashboard.tsx`)
- Files MUST use kebab-case for non-components (e.g., `mock-data.ts`)
- Hooks MUST start with "use" prefix (e.g., `useAuth.ts`)
- Types and interfaces MUST use PascalCase (e.g., `Trip`, `UserRole`)
- Constants MUST use UPPER_SNAKE_CASE (e.g., `API_BASE_URL`)

## Documentation Requirements

### Code Documentation
- All public functions MUST have JSDoc comments
- Complex algorithms MUST be explained with inline comments
- API endpoints MUST be documented with request/response examples
- Component props MUST be documented with TypeScript interfaces
- README files MUST be maintained for each major feature

### User Documentation
- Admin features MUST have user guides with screenshots
- API documentation MUST be generated automatically
- Deployment procedures MUST be documented step-by-step
- Troubleshooting guides MUST be maintained
- Change logs MUST be updated with each release

## Review Process

### Code Reviews
- All code MUST be reviewed by at least one other developer
- Security-sensitive changes MUST be reviewed by a senior developer
- Performance-critical changes MUST include performance testing
- UI changes MUST be reviewed for luxury brand consistency
- Documentation updates MUST be reviewed for accuracy

### Quality Gates
- All tests MUST pass before merging
- Code coverage MUST meet minimum thresholds
- Security scans MUST pass without critical issues
- Performance benchmarks MUST be maintained
- Accessibility standards MUST be verified

This document serves as the foundation for all development work on the Opulence platform. Adherence to these standards ensures we maintain the luxury experience expected by our VVIP clientele while building a robust, secure, and scalable platform.