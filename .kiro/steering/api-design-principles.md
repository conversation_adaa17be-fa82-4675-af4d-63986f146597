---
inclusion: always
---

# API Design Principles

## Overview

These principles guide the design and implementation of all APIs within the Opulence platform, ensuring consistency, security, and partner-friendliness. Every API must be designed to support luxury travel partners while maintaining the highest standards of performance and reliability.

## RESTful API Standards

### URL Structure and Naming

Follow consistent URL patterns:

```typescript
// ✅ CORRECT: RESTful URL structure
GET    /api/v1/partners                    // List all partners
GET    /api/v1/partners/{id}               // Get specific partner
POST   /api/v1/partners                    // Create new partner
PUT    /api/v1/partners/{id}               // Update entire partner
PATCH  /api/v1/partners/{id}               // Partial partner update
DELETE /api/v1/partners/{id}               // Delete partner

// Nested resources
GET    /api/v1/partners/{id}/services      // Get partner's services
POST   /api/v1/partners/{id}/services      // Create service for partner

// ❌ WRONG: Inconsistent naming
GET    /api/v1/getPartners
POST   /api/v1/createPartner
GET    /api/v1/partner-services/{partnerId}
```

### HTTP Methods and Status Codes

Use appropriate HTTP methods and return correct status codes:

```typescript
// ✅ CORRECT: Proper HTTP method usage
class PartnerController {
  // GET - Retrieve data (200, 404)
  async getPartner(id: string): Promise<APIResponse<Partner>> {
    const partner = await this.partnerService.findById(id);
    if (!partner) {
      return { status: 404, error: { code: 'PARTNER_NOT_FOUND', message: 'Partner not found' } };
    }
    return { status: 200, data: partner };
  }

  // POST - Create new resource (201, 400, 409)
  async createPartner(data: CreatePartnerRequest): Promise<APIResponse<Partner>> {
    try {
      const partner = await this.partnerService.create(data);
      return { status: 201, data: partner };
    } catch (error) {
      if (error.code === 'DUPLICATE_EMAIL') {
        return { status: 409, error: { code: 'PARTNER_EXISTS', message: 'Partner with this email already exists' } };
      }
      return { status: 400, error: { code: 'VALIDATION_ERROR', message: error.message } };
    }
  }

  // PUT - Replace entire resource (200, 404)
  async updatePartner(id: string, data: UpdatePartnerRequest): Promise<APIResponse<Partner>> {
    const partner = await this.partnerService.update(id, data);
    return { status: 200, data: partner };
  }

  // DELETE - Remove resource (204, 404)
  async deletePartner(id: string): Promise<APIResponse<void>> {
    await this.partnerService.delete(id);
    return { status: 204 };
  }
}
```

## Request and Response Standards

### Consistent Response Format

All API responses MUST follow this structure:

```typescript
interface APIResponse<T = any> {
  status: number;
  data?: T;
  error?: APIError;
  metadata: {
    requestId: string;
    timestamp: string;
    processingTime: number;
    rateLimit?: RateLimitInfo;
    pagination?: PaginationInfo;
  };
}

interface APIError {
  code: string;
  message: string;
  details?: any;
  documentation?: string;
}

// ✅ CORRECT: Success response
{
  "status": 200,
  "data": {
    "id": "partner_123",
    "name": "Royal Mansour",
    "type": "hotel",
    "location": "Marrakech, Morocco"
  },
  "metadata": {
    "requestId": "req_abc123",
    "timestamp": "2024-01-15T10:30:00Z",
    "processingTime": 45
  }
}

// ✅ CORRECT: Error response
{
  "status": 400,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid partner data provided",
    "details": {
      "email": "Email format is invalid",
      "phone": "Phone number is required"
    },
    "documentation": "https://docs.opulence.com/api/errors#validation-error"
  },
  "metadata": {
    "requestId": "req_def456",
    "timestamp": "2024-01-15T10:30:00Z",
    "processingTime": 12
  }
}
```

### Input Validation

Implement comprehensive input validation:

```typescript
import { z } from 'zod';

// ✅ CORRECT: Comprehensive validation schema
const CreatePartnerSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  phone: z.string().regex(/^\+[1-9]\d{1,14}$/),
  type: z.enum(['hotel', 'restaurant', 'activity', 'transport', 'spa']),
  location: z.object({
    address: z.string().min(10),
    city: z.string().min(2),
    country: z.string().length(2), // ISO country code
    coordinates: z.object({
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180)
    })
  }),
  services: z.array(z.object({
    name: z.string().min(2),
    description: z.string().min(10),
    pricing: z.object({
      currency: z.string().length(3), // ISO currency code
      basePrice: z.number().positive(),
      priceType: z.enum(['per_person', 'per_group', 'per_night', 'per_hour'])
    })
  })).optional()
});

// Validation middleware
const validateRequest = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          status: 400,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Request validation failed',
            details: error.errors.reduce((acc, err) => {
              acc[err.path.join('.')] = err.message;
              return acc;
            }, {} as Record<string, string>)
          },
          metadata: {
            requestId: req.headers['x-request-id'],
            timestamp: new Date().toISOString(),
            processingTime: 0
          }
        });
      }
      next(error);
    }
  };
};
```

## Authentication and Authorization

### API Key Authentication

Implement secure API key authentication:

```typescript
interface APIKey {
  id: string;
  partnerId: string;
  key: string; // Hashed
  scopes: string[];
  rateLimit: RateLimitConfig;
  expiresAt?: Date;
  lastUsed?: Date;
  isActive: boolean;
}

// ✅ CORRECT: API key validation
const authenticateAPIKey = async (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers['x-api-key'] as string;
  
  if (!apiKey) {
    return res.status(401).json({
      status: 401,
      error: {
        code: 'MISSING_API_KEY',
        message: 'API key is required',
        documentation: 'https://docs.opulence.com/api/authentication'
      }
    });
  }

  try {
    const keyData = await validateAPIKey(apiKey);
    if (!keyData || !keyData.isActive) {
      return res.status(401).json({
        status: 401,
        error: {
          code: 'INVALID_API_KEY',
          message: 'Invalid or inactive API key'
        }
      });
    }

    // Check rate limits
    const rateLimitResult = await checkRateLimit(keyData);
    if (!rateLimitResult.allowed) {
      return res.status(429).json({
        status: 429,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Rate limit exceeded',
          details: {
            limit: rateLimitResult.limit,
            remaining: rateLimitResult.remaining,
            resetTime: rateLimitResult.resetTime
          }
        }
      });
    }

    req.apiKey = keyData;
    next();
  } catch (error) {
    return res.status(500).json({
      status: 500,
      error: {
        code: 'AUTHENTICATION_ERROR',
        message: 'Authentication service error'
      }
    });
  }
};
```

### Scope-Based Authorization

Implement granular permissions:

```typescript
// ✅ CORRECT: Scope-based authorization
const requireScopes = (requiredScopes: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userScopes = req.apiKey?.scopes || [];
    
    const hasRequiredScopes = requiredScopes.every(scope => 
      userScopes.includes(scope) || userScopes.includes('admin')
    );

    if (!hasRequiredScopes) {
      return res.status(403).json({
        status: 403,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Insufficient permissions for this operation',
          details: {
            required: requiredScopes,
            provided: userScopes
          }
        }
      });
    }

    next();
  };
};

// Usage
router.get('/partners', authenticateAPIKey, requireScopes(['partners:read']), getPartners);
router.post('/partners', authenticateAPIKey, requireScopes(['partners:write']), createPartner);
router.delete('/partners/:id', authenticateAPIKey, requireScopes(['partners:delete']), deletePartner);
```

## Error Handling Excellence

### Comprehensive Error Classification

Define clear error categories:

```typescript
enum ErrorCode {
  // Client Errors (4xx)
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Server Errors (5xx)
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  
  // Business Logic Errors
  BOOKING_CONFLICT = 'BOOKING_CONFLICT',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  AVAILABILITY_CHANGED = 'AVAILABILITY_CHANGED',
  CAPACITY_EXCEEDED = 'CAPACITY_EXCEEDED'
}

class APIError extends Error {
  constructor(
    public code: ErrorCode,
    public message: string,
    public statusCode: number,
    public details?: any,
    public documentation?: string
  ) {
    super(message);
    this.name = 'APIError';
  }

  toResponse(): APIResponse<never> {
    return {
      status: this.statusCode,
      error: {
        code: this.code,
        message: this.message,
        details: this.details,
        documentation: this.documentation
      },
      metadata: {
        requestId: '', // Will be filled by middleware
        timestamp: new Date().toISOString(),
        processingTime: 0
      }
    };
  }
}
```

### Global Error Handler

Implement centralized error handling:

```typescript
// ✅ CORRECT: Global error handler
const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
  const requestId = req.headers['x-request-id'] as string;
  const startTime = req.startTime || Date.now();
  const processingTime = Date.now() - startTime;

  // Log error for monitoring
  logger.error('API Error', {
    requestId,
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    userAgent: req.headers['user-agent'],
    ip: req.ip
  });

  if (error instanceof APIError) {
    const response = error.toResponse();
    response.metadata.requestId = requestId;
    response.metadata.processingTime = processingTime;
    return res.status(error.statusCode).json(response);
  }

  // Handle unexpected errors
  const response: APIResponse<never> = {
    status: 500,
    error: {
      code: ErrorCode.INTERNAL_ERROR,
      message: 'An unexpected error occurred',
      documentation: 'https://docs.opulence.com/api/errors#internal-error'
    },
    metadata: {
      requestId,
      timestamp: new Date().toISOString(),
      processingTime
    }
  };

  res.status(500).json(response);
};
```

## Pagination and Filtering

### Consistent Pagination

Implement cursor-based pagination for performance:

```typescript
interface PaginationParams {
  limit?: number;
  cursor?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    hasNext: boolean;
    hasPrevious: boolean;
    nextCursor?: string;
    previousCursor?: string;
    totalCount?: number;
  };
}

// ✅ CORRECT: Cursor-based pagination
const getPartners = async (req: Request, res: Response) => {
  const { limit = 20, cursor, sort = 'createdAt', order = 'desc' } = req.query as PaginationParams;
  
  const result = await partnerService.findMany({
    limit: Math.min(limit, 100), // Cap at 100
    cursor,
    sort,
    order,
    filters: req.query.filters
  });

  const response: APIResponse<PaginatedResponse<Partner>> = {
    status: 200,
    data: {
      data: result.partners,
      pagination: {
        hasNext: result.hasNext,
        hasPrevious: result.hasPrevious,
        nextCursor: result.nextCursor,
        previousCursor: result.previousCursor,
        totalCount: result.totalCount
      }
    },
    metadata: {
      requestId: req.headers['x-request-id'] as string,
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - req.startTime
    }
  };

  res.json(response);
};
```

### Advanced Filtering

Support complex filtering operations:

```typescript
interface FilterParams {
  type?: string[];
  location?: {
    city?: string;
    country?: string;
    radius?: number;
    coordinates?: [number, number];
  };
  pricing?: {
    min?: number;
    max?: number;
    currency?: string;
  };
  availability?: {
    from?: string;
    to?: string;
  };
  qualityScore?: {
    min?: number;
  };
}

// ✅ CORRECT: Advanced filtering
const buildFilters = (params: FilterParams) => {
  const filters: any = {};

  if (params.type?.length) {
    filters.type = { $in: params.type };
  }

  if (params.location) {
    if (params.location.city) {
      filters['location.city'] = { $regex: params.location.city, $options: 'i' };
    }
    if (params.location.coordinates && params.location.radius) {
      filters['location.coordinates'] = {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: params.location.coordinates
          },
          $maxDistance: params.location.radius * 1000 // Convert km to meters
        }
      };
    }
  }

  if (params.pricing) {
    const pricingFilter: any = {};
    if (params.pricing.min) pricingFilter.$gte = params.pricing.min;
    if (params.pricing.max) pricingFilter.$lte = params.pricing.max;
    if (Object.keys(pricingFilter).length) {
      filters['services.pricing.basePrice'] = pricingFilter;
    }
  }

  return filters;
};
```

## Webhook Implementation

### Webhook Registration and Management

```typescript
interface WebhookRegistration {
  url: string;
  events: WebhookEventType[];
  secret: string;
  retryPolicy: {
    maxAttempts: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
  filters?: Record<string, any>;
}

// ✅ CORRECT: Webhook system
class WebhookService {
  async register(partnerId: string, webhook: WebhookRegistration): Promise<string> {
    // Validate webhook URL
    await this.validateWebhookUrl(webhook.url);
    
    const webhookId = generateId();
    await this.webhookRepository.create({
      id: webhookId,
      partnerId,
      ...webhook,
      isActive: true,
      createdAt: new Date()
    });

    return webhookId;
  }

  async trigger(event: WebhookEvent): Promise<void> {
    const webhooks = await this.webhookRepository.findByEvent(event.type);
    
    const deliveryPromises = webhooks.map(webhook => 
      this.deliverWebhook(webhook, event)
    );

    await Promise.allSettled(deliveryPromises);
  }

  private async deliverWebhook(webhook: Webhook, event: WebhookEvent): Promise<void> {
    const payload = {
      id: generateId(),
      event: event.type,
      data: event.data,
      timestamp: event.timestamp,
      webhook_id: webhook.id
    };

    const signature = this.generateSignature(payload, webhook.secret);
    
    try {
      await fetch(webhook.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Opulence-Signature': signature,
          'X-Opulence-Event': event.type,
          'User-Agent': 'Opulence-Webhooks/1.0'
        },
        body: JSON.stringify(payload),
        timeout: 30000
      });

      await this.logDelivery(webhook.id, payload.id, 'success');
    } catch (error) {
      await this.logDelivery(webhook.id, payload.id, 'failed', error.message);
      await this.scheduleRetry(webhook, payload);
    }
  }
}
```

## Performance and Caching

### Response Caching

Implement intelligent caching:

```typescript
// ✅ CORRECT: Cache implementation
const cacheMiddleware = (ttl: number = 300) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const cacheKey = `api:${req.method}:${req.originalUrl}:${JSON.stringify(req.query)}`;
    
    try {
      const cached = await redis.get(cacheKey);
      if (cached) {
        const response = JSON.parse(cached);
        response.metadata.cached = true;
        response.metadata.cacheAge = Date.now() - new Date(response.metadata.timestamp).getTime();
        return res.json(response);
      }
    } catch (error) {
      // Cache miss, continue to handler
    }

    // Override res.json to cache the response
    const originalJson = res.json;
    res.json = function(body: any) {
      if (res.statusCode === 200) {
        redis.setex(cacheKey, ttl, JSON.stringify(body));
      }
      return originalJson.call(this, body);
    };

    next();
  };
};

// Usage
router.get('/partners', cacheMiddleware(600), getPartners); // Cache for 10 minutes
```

## Documentation Standards

### OpenAPI Specification

All APIs MUST be documented using OpenAPI 3.0:

```yaml
# ✅ CORRECT: OpenAPI documentation
openapi: 3.0.0
info:
  title: Opulence Partner API
  version: 1.0.0
  description: API for luxury travel partners to integrate with Opulence platform
  contact:
    name: API Support
    email: <EMAIL>
    url: https://docs.opulence.com

servers:
  - url: https://api.opulence.com/v1
    description: Production server
  - url: https://api-staging.opulence.com/v1
    description: Staging server

paths:
  /partners:
    get:
      summary: List partners
      description: Retrieve a paginated list of partners
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: cursor
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PartnerListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'

components:
  schemas:
    Partner:
      type: object
      required:
        - id
        - name
        - email
        - type
      properties:
        id:
          type: string
          example: "partner_123"
        name:
          type: string
          example: "Royal Mansour"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        type:
          type: string
          enum: [hotel, restaurant, activity, transport, spa]
```

Remember: Great APIs are not just functional—they're intuitive, reliable, and delightful to work with. Every endpoint should feel like a natural extension of the luxury experience we provide to our VVIP clients.