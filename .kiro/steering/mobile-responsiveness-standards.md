---
inclusion: always
---

# Mobile Responsiveness Standards

## Overview

These standards ensure that Opulence delivers an exceptional ultra-luxury experience across all devices, from mobile phones to large desktop displays. Every component and feature must be designed with mobile-first principles while maintaining the sophisticated aesthetic that defines the VVIP experience.

## Responsive Design Principles

### Mobile-First Approach

All CSS and components MUST be designed mobile-first:

```css
/* ✅ CORRECT: Mobile-first approach */
.luxury-card {
  padding: 1rem;           /* Mobile base */
  font-size: 0.875rem;     /* Mobile base */
}

@media (min-width: 768px) {
  .luxury-card {
    padding: 2rem;         /* Tablet enhancement */
    font-size: 1rem;       /* Tablet enhancement */
  }
}

@media (min-width: 1024px) {
  .luxury-card {
    padding: 3rem;         /* Desktop enhancement */
    font-size: 1.125rem;   /* Desktop enhancement */
  }
}
```

### Tailwind Breakpoint Usage

Use Tailwind's responsive prefixes consistently:

```tsx
// ✅ CORRECT: Progressive enhancement
<div className="p-4 md:p-8 lg:p-12 xl:p-16">
  <h1 className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-serif">
    Luxury Experience
  </h1>
</div>

// ❌ WRONG: Desktop-first approach
<div className="p-16 lg:p-12 md:p-8 p-4">
```

## Touch Optimization Standards

### Touch Target Sizes

All interactive elements MUST meet minimum touch target requirements:

```tsx
// ✅ CORRECT: Adequate touch targets
<button className="min-h-[44px] min-w-[44px] p-3 rounded-xl">
  <Icon className="w-6 h-6" />
</button>

// ❌ WRONG: Too small for touch
<button className="p-1 rounded">
  <Icon className="w-4 h-4" />
</button>
```

### Touch Target Spacing

Maintain adequate spacing between interactive elements:

```tsx
// ✅ CORRECT: Proper spacing
<div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-6">
  <Button>Primary Action</Button>
  <Button variant="outline">Secondary Action</Button>
</div>
```

### Gesture Support

Implement appropriate gestures for mobile interactions:

```tsx
// ✅ CORRECT: Swipe gesture implementation
const useSwipeGesture = (onSwipe: (direction: string) => void) => {
  // Implementation for swipe detection
  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
  };
};
```

## Typography Responsiveness

### Responsive Typography Scale

Use the responsive typography system:

```tsx
// ✅ CORRECT: Responsive typography
<h1 className="text-display-sm md:text-display-md lg:text-display-lg">
  Luxury Travel Experience
</h1>

<p className="text-body-md md:text-body-lg">
  Discover extraordinary destinations with our VVIP service.
</p>
```

### Reading Comfort

Ensure optimal reading experience across devices:

```tsx
// ✅ CORRECT: Comfortable reading width
<div className="max-w-none md:max-w-2xl lg:max-w-4xl mx-auto">
  <p className="leading-relaxed md:leading-loose">
    Long-form content that remains comfortable to read...
  </p>
</div>
```

## Layout Adaptability

### Grid Systems

Use responsive grid patterns:

```tsx
// ✅ CORRECT: Adaptive grid
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
  {items.map(item => (
    <Card key={item.id}>{item.content}</Card>
  ))}
</div>
```

### Navigation Patterns

Implement mobile-appropriate navigation:

```tsx
// ✅ CORRECT: Responsive navigation
const Navigation = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden lg:flex space-x-8">
        {navItems.map(item => (
          <NavLink key={item.id} to={item.path}>
            {item.label}
          </NavLink>
        ))}
      </nav>
      
      {/* Mobile Navigation */}
      <div className="lg:hidden">
        <button
          onClick={() => setMobileMenuOpen(true)}
          className="p-2 rounded-xl bg-platinum-800"
        >
          <Menu className="w-6 h-6" />
        </button>
        
        <MobileMenu 
          isOpen={mobileMenuOpen}
          onClose={() => setMobileMenuOpen(false)}
          items={navItems}
        />
      </div>
    </>
  );
};
```

## Image and Media Optimization

### Responsive Images

Implement responsive image loading:

```tsx
// ✅ CORRECT: Responsive images
<img
  src={image.src}
  srcSet={`
    ${image.mobile} 375w,
    ${image.tablet} 768w,
    ${image.desktop} 1200w,
    ${image.large} 1920w
  `}
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  alt={image.alt}
  className="w-full h-auto object-cover rounded-2xl"
  loading="lazy"
/>
```

### Video Optimization

Ensure videos work well on mobile:

```tsx
// ✅ CORRECT: Mobile-optimized video
<video
  className="w-full h-auto rounded-2xl"
  controls
  playsInline
  preload="metadata"
  poster={videoPoster}
>
  <source src={videoSrc} type="video/mp4" />
  Your browser does not support the video tag.
</video>
```

## Performance Standards

### Mobile Performance Targets

- **First Contentful Paint**: < 2 seconds on 3G
- **Largest Contentful Paint**: < 3 seconds on 3G
- **Time to Interactive**: < 4 seconds on 3G
- **Cumulative Layout Shift**: < 0.1

### Code Splitting

Implement proper code splitting for mobile:

```tsx
// ✅ CORRECT: Lazy loading for mobile optimization
const HeavyComponent = lazy(() => import('./HeavyComponent'));

const MobileOptimizedPage = () => {
  const isMobile = useMediaQuery('(max-width: 768px)');
  
  return (
    <div>
      {isMobile ? (
        <LightweightMobileComponent />
      ) : (
        <Suspense fallback={<LoadingSpinner />}>
          <HeavyComponent />
        </Suspense>
      )}
    </div>
  );
};
```

## Testing Requirements

### Device Testing Matrix

All components MUST be tested on:

- **Mobile Phones**: iPhone 12/13/14, Samsung Galaxy S21/S22, Google Pixel 6/7
- **Tablets**: iPad Air, iPad Pro, Samsung Galaxy Tab S7
- **Desktop**: 1920x1080, 2560x1440, 3840x2160

### Browser Testing

Test across major browsers:
- Safari (iOS)
- Chrome (Android/Desktop)
- Firefox (Desktop)
- Edge (Desktop)

### Accessibility Testing

Ensure mobile accessibility:

```tsx
// ✅ CORRECT: Mobile accessibility
<button
  className="min-h-[44px] min-w-[44px] p-3 rounded-xl"
  aria-label="Close dialog"
  onClick={onClose}
>
  <X className="w-6 h-6" />
</button>
```

## Common Anti-Patterns to Avoid

### ❌ Desktop-First Design

```css
/* WRONG: Desktop-first approach */
.component {
  padding: 3rem;
  font-size: 1.5rem;
}

@media (max-width: 768px) {
  .component {
    padding: 1rem;
    font-size: 1rem;
  }
}
```

### ❌ Fixed Dimensions

```tsx
// WRONG: Fixed dimensions that break on mobile
<div className="w-[800px] h-[600px]">
  Content
</div>
```

### ❌ Hover-Dependent Functionality

```tsx
// WRONG: Functionality only available on hover
<div className="group">
  <div className="opacity-0 group-hover:opacity-100">
    Important action button
  </div>
</div>
```

### ❌ Small Touch Targets

```tsx
// WRONG: Touch targets too small
<button className="p-1 text-xs">
  <Icon className="w-3 h-3" />
</button>
```

## Implementation Checklist

Before deploying any component, verify:

- [ ] Mobile-first CSS approach used
- [ ] All touch targets are at least 44px
- [ ] Typography scales appropriately
- [ ] Images are responsive and optimized
- [ ] Navigation works on mobile
- [ ] Performance targets are met
- [ ] Cross-device testing completed
- [ ] Accessibility requirements satisfied
- [ ] Luxury aesthetic maintained across all devices

## Tools and Resources

### Development Tools
- Chrome DevTools Device Mode
- Firefox Responsive Design Mode
- Safari Web Inspector
- React DevTools

### Testing Tools
- BrowserStack for cross-device testing
- Lighthouse for performance auditing
- axe-core for accessibility testing
- WebPageTest for performance analysis

### Design Resources
- Material Design touch target guidelines
- Apple Human Interface Guidelines
- WCAG 2.1 accessibility standards
- Core Web Vitals metrics

Remember: Mobile responsiveness is not just about making things smaller—it's about creating an optimized experience that feels natural and luxurious on every device.