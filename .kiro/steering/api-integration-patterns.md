---
inclusion: fileMatch
fileMatchPattern: '**/api/**'
---

# API Integration Patterns for Opulence Platform

## Overview

This document defines the standard patterns and practices for API integration in the Opulence platform. All API-related code must follow these patterns to ensure consistency, security, and maintainability.

## API Client Architecture

### Base API Client Structure
```typescript
// lib/api/client.ts
export class ApiClient {
  private baseURL: string;
  private authToken: string | null = null;
  
  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }
  
  setAuthToken(token: string) {
    this.authToken = token;
  }
  
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    // Implementation with error handling, retries, and logging
  }
}
```

### Service Layer Pattern
Each feature MUST have its own service class that extends the base API client:

```typescript
// lib/api/services/TripService.ts
export class TripService extends ApiClient {
  async getTrips(clientId: string): Promise<Trip[]> {
    const response = await this.request<Trip[]>(`/trips?clientId=${clientId}`);
    return response.data;
  }
  
  async createTrip(tripData: CreateTripRequest): Promise<Trip> {
    const response = await this.request<Trip>('/trips', {
      method: 'POST',
      body: JSON.stringify(tripData),
    });
    return response.data;
  }
}
```

## Error Handling Standards

### Error Response Format
All API errors MUST follow this standardized format:

```typescript
interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  requestId: string;
}
```

### Error Handling Implementation
```typescript
// lib/api/errors.ts
export class ApiErrorHandler {
  static handle(error: ApiError): void {
    switch (error.code) {
      case 'AUTHENTICATION_FAILED':
        // Redirect to login
        break;
      case 'INSUFFICIENT_PERMISSIONS':
        // Show permission error
        break;
      case 'RATE_LIMIT_EXCEEDED':
        // Implement backoff strategy
        break;
      default:
        // Generic error handling
    }
  }
}
```

## Authentication Patterns

### JWT Token Management
```typescript
// lib/auth/tokenManager.ts
export class TokenManager {
  private static instance: TokenManager;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  
  async getValidToken(): Promise<string> {
    if (this.isTokenExpired(this.accessToken)) {
      await this.refreshAccessToken();
    }
    return this.accessToken!;
  }
  
  private async refreshAccessToken(): Promise<void> {
    // Implement token refresh logic
  }
}
```

### Request Interceptors
All API requests MUST include authentication headers:

```typescript
// lib/api/interceptors.ts
export const authInterceptor = (request: RequestInit): RequestInit => {
  const token = TokenManager.getInstance().getValidToken();
  return {
    ...request,
    headers: {
      ...request.headers,
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };
};
```

## Data Validation Patterns

### Request Validation
All API requests MUST be validated using Zod schemas:

```typescript
// lib/api/schemas/tripSchemas.ts
import { z } from 'zod';

export const CreateTripSchema = z.object({
  title: z.string().min(1).max(200),
  destination: z.string().min(1),
  startDate: z.date(),
  endDate: z.date(),
  clientId: z.string().uuid(),
});

export type CreateTripRequest = z.infer<typeof CreateTripSchema>;
```

### Response Validation
API responses MUST be validated before use:

```typescript
// lib/api/validation.ts
export const validateApiResponse = <T>(
  data: unknown,
  schema: z.ZodSchema<T>
): T => {
  try {
    return schema.parse(data);
  } catch (error) {
    throw new ApiValidationError('Invalid API response format', error);
  }
};
```

## Caching Strategies

### React Query Integration
All data fetching MUST use React Query for caching and synchronization:

```typescript
// hooks/api/useTrips.ts
export const useTrips = (clientId: string) => {
  return useQuery({
    queryKey: ['trips', clientId],
    queryFn: () => tripService.getTrips(clientId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};
```

### Cache Invalidation
Cache invalidation MUST be handled consistently:

```typescript
// hooks/api/useTripMutations.ts
export const useCreateTrip = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (tripData: CreateTripRequest) => 
      tripService.createTrip(tripData),
    onSuccess: (newTrip) => {
      queryClient.invalidateQueries(['trips', newTrip.clientId]);
      queryClient.setQueryData(['trip', newTrip.id], newTrip);
    },
  });
};
```

## Real-time Integration

### WebSocket Connection Management
```typescript
// lib/websocket/connection.ts
export class WebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  
  connect(url: string): void {
    this.ws = new WebSocket(url);
    this.setupEventHandlers();
  }
  
  private setupEventHandlers(): void {
    if (!this.ws) return;
    
    this.ws.onopen = () => {
      this.reconnectAttempts = 0;
      this.authenticate();
    };
    
    this.ws.onmessage = (event) => {
      this.handleMessage(JSON.parse(event.data));
    };
    
    this.ws.onclose = () => {
      this.handleReconnection();
    };
  }
}
```

### Event Handling
Real-time events MUST be handled through a centralized event system:

```typescript
// lib/events/eventManager.ts
export class EventManager {
  private listeners: Map<string, Function[]> = new Map();
  
  subscribe(event: string, callback: Function): () => void {
    const callbacks = this.listeners.get(event) || [];
    callbacks.push(callback);
    this.listeners.set(event, callbacks);
    
    return () => this.unsubscribe(event, callback);
  }
  
  emit(event: string, data: any): void {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => callback(data));
  }
}
```

## Rate Limiting and Retry Logic

### Exponential Backoff
```typescript
// lib/api/retryLogic.ts
export class RetryManager {
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxAttempts) break;
        
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }
}
```

### Rate Limit Handling
```typescript
// lib/api/rateLimiting.ts
export class RateLimitManager {
  private requestCounts: Map<string, number> = new Map();
  private resetTimes: Map<string, number> = new Map();
  
  async checkRateLimit(endpoint: string): Promise<boolean> {
    const now = Date.now();
    const resetTime = this.resetTimes.get(endpoint) || 0;
    
    if (now > resetTime) {
      this.requestCounts.set(endpoint, 0);
      this.resetTimes.set(endpoint, now + 60000); // 1 minute window
    }
    
    const currentCount = this.requestCounts.get(endpoint) || 0;
    if (currentCount >= this.getRateLimit(endpoint)) {
      return false;
    }
    
    this.requestCounts.set(endpoint, currentCount + 1);
    return true;
  }
}
```

## Security Patterns

### Request Signing
High-value operations MUST include request signing:

```typescript
// lib/security/requestSigning.ts
export class RequestSigner {
  static sign(
    method: string,
    url: string,
    body: string,
    timestamp: number,
    secretKey: string
  ): string {
    const message = `${method}\n${url}\n${body}\n${timestamp}`;
    return crypto.createHmac('sha256', secretKey)
      .update(message)
      .digest('hex');
  }
}
```

### Sensitive Data Handling
```typescript
// lib/security/dataProtection.ts
export class DataProtection {
  static sanitizeForLogging(data: any): any {
    const sensitiveFields = [
      'password', 'token', 'creditCard', 'ssn', 'passport'
    ];
    
    return this.deepClone(data, (key, value) => {
      if (sensitiveFields.includes(key.toLowerCase())) {
        return '[REDACTED]';
      }
      return value;
    });
  }
}
```

## Testing Patterns

### API Mocking
```typescript
// lib/testing/apiMocks.ts
export class ApiMockManager {
  private mocks: Map<string, any> = new Map();
  
  mockEndpoint(endpoint: string, response: any): void {
    this.mocks.set(endpoint, response);
  }
  
  getMockResponse(endpoint: string): any {
    return this.mocks.get(endpoint);
  }
  
  reset(): void {
    this.mocks.clear();
  }
}
```

### Integration Testing
```typescript
// tests/api/tripService.test.ts
describe('TripService', () => {
  beforeEach(() => {
    ApiMockManager.reset();
  });
  
  it('should fetch trips for a client', async () => {
    const mockTrips = [/* mock data */];
    ApiMockManager.mockEndpoint('/trips', mockTrips);
    
    const trips = await tripService.getTrips('client-id');
    expect(trips).toEqual(mockTrips);
  });
});
```

## Performance Optimization

### Request Batching
```typescript
// lib/api/batching.ts
export class RequestBatcher {
  private batchQueue: Map<string, any[]> = new Map();
  private batchTimeout: Map<string, NodeJS.Timeout> = new Map();
  
  addToBatch(endpoint: string, request: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const queue = this.batchQueue.get(endpoint) || [];
      queue.push({ request, resolve, reject });
      this.batchQueue.set(endpoint, queue);
      
      this.scheduleBatchExecution(endpoint);
    });
  }
}
```

### Response Compression
```typescript
// lib/api/compression.ts
export const compressionMiddleware = (response: Response): Response => {
  if (response.headers.get('content-encoding') === 'gzip') {
    // Handle compressed responses
  }
  return response;
};
```

## Monitoring and Logging

### API Metrics Collection
```typescript
// lib/monitoring/apiMetrics.ts
export class ApiMetrics {
  static recordRequest(
    endpoint: string,
    method: string,
    duration: number,
    status: number
  ): void {
    // Send metrics to monitoring service
    this.sendMetric({
      type: 'api_request',
      endpoint,
      method,
      duration,
      status,
      timestamp: Date.now(),
    });
  }
}
```

### Error Logging
```typescript
// lib/logging/errorLogger.ts
export class ErrorLogger {
  static logApiError(
    error: ApiError,
    context: {
      endpoint: string;
      method: string;
      userId?: string;
    }
  ): void {
    const logEntry = {
      level: 'error',
      message: error.message,
      error: error,
      context,
      timestamp: new Date().toISOString(),
    };
    
    // Send to logging service
    this.sendLog(logEntry);
  }
}
```

These patterns ensure consistent, secure, and maintainable API integration throughout the Opulence platform while maintaining the high standards expected for ultra-luxury travel applications.